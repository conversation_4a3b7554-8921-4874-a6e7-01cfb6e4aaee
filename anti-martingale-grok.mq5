//+------------------------------------------------------------------+
//|                                      anti-martingale-fibonacci.mq5 |
//|                                  Copyright 2023, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.20" // Updated version with fixes and improvements

#include <Trade\Trade.mqh>

//--- Input parameters
input bool autoCheckTrend = false;          // Use indicators for trend detection
input bool isSkipBonus = false;            // Skip bonus profit target
input string manualStartTrend = "BUY";     // Manual trend direction (BUY/SELL)
input double initAccountBalance = 4000;    // Initial account balance (0 for actual balance)
input double takeProfit = 0.005;           // Profit target as % of balance (0.5%)
input double targetDay = 400;              // Daily profit target
input double initialLot = 0.05;            // Initial lot size
input double maxLot = 1.0;                 // Maximum lot size
input int bonusTimeoutSeconds = 100;       // Bonus mode timeout (seconds)
input double riskPercent = 1.0;            // Risk per trade as % of equity
input int maFastPeriod = 20;               // Fast MA period
input int maSlowPeriod = 50;               // Slow MA period
input int adxPeriod = 14;                  // ADX period
input int rsiPeriod = 14;                  // RSI period
input int bbPeriod = 20;                   // Bollinger Bands period
input double inputADXThreshold = 25.0;      // ADX threshold for strong trend
input double inputDISeparation = 5.0;       // Minimum +DI/-DI separation
input double inputSidewayADXThreshold = 25.0; // ADX threshold for sideways market
input double inputSidewayBBThreshold = 0.025; // Bollinger Bands width threshold for sideways
input int inputTrendLookback = 3;           // Number of bars to check for trend persistence
input ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT; // Indicator timeframe

//--- Global variables
int FiboSeq[10] = {1, 1, 2, 3, 5, 8, 13, 21, 34, 55}; // Fibonacci sequence
bool g_isBuyTrend = manualStartTrend == "BUY";
bool g_isFirstTrade = true;
bool g_isFinishing = false;
double g_targetDay = targetDay;
double g_collectTargetDay = 0;
double g_targetProfit = 0;
double g_accountBalance = 0;
datetime g_lastPrintTime = 0;
datetime g_bonusPrintTime = 0;
datetime lastTradingDay = 0;
ulong currentTicketId = 0;
int count = 1;
bool startBonus = false;
bool startBonusDone = false;
bool skipBonus = isSkipBonus;
datetime timeBonus = 0;
int adxHandle = INVALID_HANDLE;
int atrHandle = INVALID_HANDLE;

CTrade Trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit()
{
    g_accountBalance = initAccountBalance == 0 ? AccountInfoDouble(ACCOUNT_BALANCE) : initAccountBalance;
    g_targetProfit = g_accountBalance * takeProfit;

    Print("******************************START_INIT******************************");
    Print("Target day: ", g_targetDay);
    Print("Initial lot size: ", initialLot);
    Print("Maximum lot size: ", maxLot);
    Print("Risk per trade: ", riskPercent, "%");
    Print("Account balance: ", g_accountBalance);
    Print("Profit target per trade: ", g_targetProfit);
    Print("******************************END_INIT******************************");

    // Initialize indicator handles
    adxHandle = iADX(Symbol(), timeframe, adxPeriod);
    if(adxHandle == INVALID_HANDLE)
    {
        Print("Error creating ADX indicator handle");
        return INIT_FAILED;
    }

    atrHandle = iATR(Symbol(), timeframe, 14);
    if(atrHandle == INVALID_HANDLE)
    {
        Print("Error creating ATR indicator handle");
        return INIT_FAILED;
    }

    Trade.SetDeviationInPoints(10);
    Trade.SetTypeFilling(ORDER_FILLING_FOK);
    Trade.SetAsyncMode(false);

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(adxHandle != INVALID_HANDLE) IndicatorRelease(adxHandle);
    if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Reset daily profit target at the start of a new trading day
    datetime currentDay = TimeCurrent() / 86400 * 86400; // Start of current day
    if(currentDay != lastTradingDay && lastTradingDay != 0)
    {
        g_collectTargetDay = 0;
        g_isFinishing = false;
        count = 1;
        g_isFirstTrade = true;
        resetPairs();
        Print("***************NEW_TRADING_DAY***************");
    }
    lastTradingDay = currentDay;

    // Stop trading if daily target is reached
    if(g_collectTargetDay >= g_targetDay)
    {
        if(!g_isFinishing)
        {
            g_isFinishing = true;
            Print("***************DONE_TARGET_DAY***************");
            Print("Current Target Day: ", g_collectTargetDay);
            printPairs();
            Print("***************DONE_TARGET_DAY***************");
        }
        return;
    }

    // Open first trade or check existing trade
    if(g_isFirstTrade)
    {
        bool orderOpened = OpenOrder();
        g_isFirstTrade = !orderOpened;
        return;
    }

    checkAndOpenAdditionalOrders();
}

//+------------------------------------------------------------------+
//| Open a new order                                                 |
//+------------------------------------------------------------------+
bool OpenOrder()
{
    int trend = autoCheckTrend ? checkTrend() : (manualStartTrend == "BUY" ? 1 : -1);
    string currentTrend = "NONE";
    bool isBuyTrend = true;
    switch(trend)
    {
        case 1:
            isBuyTrend = true;
            currentTrend = "UP";
            break;
        case -1:
            isBuyTrend = false;
            currentTrend = "DOWN";
            break;
        case 0:
            return false; // No trade if no clear trend
    }

    double currentPrice = SymbolInfoDouble(Symbol(), isBuyTrend ? SYMBOL_ASK : SYMBOL_BID);
    double newLots = CalculateLotSize();

    Print("Opening ", isBuyTrend ? "BUY" : "SELL", " order with lot size: ", newLots);

    isBuyTrend
        ? Trade.Buy(newLots, Symbol(), currentPrice, 0, 0, "Open BUY Order")
        : Trade.Sell(newLots, Symbol(), currentPrice, 0, 0, "Open SELL Order");
    currentTicketId = Trade.ResultOrder();

    if(currentTicketId > 0)
    {
        addKeyValuePair(currentTicketId, newLots, g_targetProfit, isBuyTrend ? "BUY" : "SELL", 0.0);
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Calculate lot size based on equity and Fibonacci sequence         |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double riskAmount = equity * (riskPercent / 100.0);
    double atr[];
    ArraySetAsSeries(atr, true);
    if(CopyBuffer(atrHandle, 0, 0, 1, atr) <= 0)
    {
        Print("Error copying ATR buffer");
        return initialLot; // Fallback to initial lot
    }
    double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double lotSize = riskAmount / (atr[0] * tickValue * FiboSeq[count]);
    return MathMin(lotSize, maxLot);
}

//+------------------------------------------------------------------+
//| Calculate dynamic stop-loss based on ATR                         |
//+------------------------------------------------------------------+
double CalculateStopLoss(int fiboIndex)
{
    double atr[];
    ArraySetAsSeries(atr, true);
    if(CopyBuffer(atrHandle, 0, 0, 1, atr) <= 0)
    {
        Print("Error copying ATR buffer");
        return -g_targetProfit * FiboSeq[fiboIndex] * 0.5; // Fallback
    }
    double atrValue = atr[0];
    return -g_targetProfit * FiboSeq[fiboIndex] * (0.5 + atrValue * 50);
}

//+------------------------------------------------------------------+
//| Check and manage open orders                                      |
//+------------------------------------------------------------------+
void checkAndOpenAdditionalOrders()
{
    if(currentTicketId == 0 || !PositionSelectByTicket(currentTicketId))
    {
        OpenOrder();
        return;
    }

    double profit = PositionGetDouble(POSITION_PROFIT);
    double targetProfit = g_targetProfit * FiboSeq[count];
    double targetSL = CalculateStopLoss(count);

    // Bonus mode
    if(startBonus)
    {
        startBonusDone = true;
        datetime currentTime = TimeCurrent();
        double atr[];
        ArraySetAsSeries(atr, true);
        if(CopyBuffer(atrHandle, 0, 0, 1, atr) <= 0)
        {
            Print("Error copying ATR buffer");
            startBonus = false; // Exit bonus on error
        }
        else
        {
            double volatilityFactor = MathMin(1.5, MathMax(1.2, 1.5 - (atr[0] * 100)));
            double targetBonusProfit = targetProfit * volatilityFactor;

            if(currentTime - g_bonusPrintTime >= 15)
            {
                Print("***************START_BONUS***************");
                Print("Profit: ", profit);
                Print("Target Bonus Profit: ", targetBonusProfit);
                Print("Positive: ", (profit >= targetBonusProfit ? "true" : "false"));
                Print("***************END_BONUS***************");
                g_bonusPrintTime = currentTime;
            }

            if(profit >= targetBonusProfit)
            {
                Trade.PositionClose(currentTicketId);
                g_collectTargetDay += profit;
                updateValuePair(currentTicketId, profit);
                count += 1;
                if(count >= ArraySize(FiboSeq)) count = 1; // Prevent overflow
                currentTicketId = 0;
                g_isFirstTrade = true; // Next trade
                startBonusDone = false;
                return;
            }
            else if(profit <= 0) // Close on loss
            {
                Trade.PositionClose(currentTicketId);
                g_collectTargetDay += profit;
                updateValuePair(currentTicketId, profit);
                count = 1; // Reset to first Fibonacci
                currentTicketId = 0;
                g_isFirstTrade = true;
                startBonusDone = false;
                return;
            }
            else if(currentTime - timeBonus >= bonusTimeoutSeconds)
            {
                Trade.PositionClose(currentTicketId);
                g_collectTargetDay += profit;
                updateValuePair(currentTicketId, profit);
                count = (profit > 0) ? count + 1 : 1; // Increment if profitable, reset if not
                if(count >= ArraySize(FiboSeq)) count = 1;
                currentTicketId = 0;
                g_isFirstTrade = true;
                startBonusDone = false;
                return;
            }
        }
        return;
    }

    // Regular profit/loss handling
    datetime currentTime = TimeCurrent();
    if(currentTime - g_lastPrintTime >= 20)
    {
        Print("***************START_DONE***************");
        Print("Profit: ", profit);
        Print("Target Profit: ", targetProfit);
        Print("Positive: ", (profit >= targetProfit ? "true" : "false"), " || Negative: ", (profit <= targetSL ? "true" : "false"));
        Print("Target Day: ", g_targetDay);
        Print("Current Target Day: ", g_collectTargetDay);
        printPairs();
        Print("***************END_DONE***************");
        g_lastPrintTime = currentTime;
    }

    if(profit >= targetProfit)
    {
        if(!skipBonus && !startBonus && !startBonusDone)
        {
            startBonus = true;
            timeBonus = TimeCurrent();
            return;
        }
        Trade.PositionClose(currentTicketId);
        g_collectTargetDay += profit;
        updateValuePair(currentTicketId, profit);
        count += 1;
        if(count >= ArraySize(FiboSeq)) count = 1; // Prevent overflow
        currentTicketId = 0;
        g_isFirstTrade = true; // Next trade
        startBonusDone = false;
        return;
    }

    if(profit <= targetSL)
    {
        Trade.PositionClose(currentTicketId);
        g_collectTargetDay += profit;
        updateValuePair(currentTicketId, profit);
        count = 1; // Reset to first Fibonacci
        currentTicketId = 0;
        g_isFirstTrade = true;
        startBonusDone = false;
    }
}

//+------------------------------------------------------------------+
//| Check trend before opening an order                              |
//+------------------------------------------------------------------+
int checkTrend()
{
    // Check for sideways market to avoid trading
    if(isSideway())
    {
        Print("***************SIDEWAY_MARKET***************");
        return 0; // No trading in sideways markets
    }

    // Get indicator values
    bool maUptrend = isUptrend();
    bool adxStrong = isStrongTrend();
    bool adxUptrend = isUptrendADX();
    bool rsiUptrend = isUptrendRSI();
    bool priceActionUptrend = isUptrendPriceAction();

    // Require strong ADX as primary condition
    if(!adxStrong)
    {
        Print("***************NO_STRONG_TREND***************");
        return 0; // No trade if ADX indicates weak trend
    }

    // Count uptrend and downtrend signals
    int uptrendCount = 0;
    if(maUptrend) uptrendCount++;
    if(adxUptrend) uptrendCount++;
    if(rsiUptrend) uptrendCount++;
    if(priceActionUptrend) uptrendCount++;

    int downtrendCount = 0;
    if(!maUptrend) downtrendCount++;
    if(!adxUptrend) downtrendCount++;
    if(!rsiUptrend) downtrendCount++;
    if(!priceActionUptrend) downtrendCount++;

    // Require at least 3 out of 4 signals to confirm trend
    if(uptrendCount >= 3)
    {
        Print("***************Trend_UP***************");
        return 1; // Uptrend confirmed
    }
    if(downtrendCount >= 3)
    {
        Print("***************Trend_DOWN***************");
        return -1; // Downtrend confirmed
    }

    Print("***************NO_CLEAR_TREND***************");
    return 0; // No clear trend
}

//+------------------------------------------------------------------+
//| Check if market is sideways                                      |
//+------------------------------------------------------------------+
bool isSideway()
{
    double adx[], plusDI[], minusDI[];
    if(!getADXValues(adx, plusDI, minusDI, 3)) // Check over 3 bars
        return false;

    double rsi[];
    ArraySetAsSeries(rsi, true);
    int rsiHandle = iRSI(Symbol(), timeframe, rsiPeriod, PRICE_CLOSE);
    if(rsiHandle == INVALID_HANDLE || CopyBuffer(rsiHandle, 0, 0, 3, rsi) <= 0)
    {
        Print("Error accessing RSI");
        return false;
    }

    double bbUpper[], bbMiddle[], bbLower[];
    ArraySetAsSeries(bbUpper, true);
    ArraySetAsSeries(bbMiddle, true);
    ArraySetAsSeries(bbLower, true);
    int bbHandle = iBands(Symbol(), timeframe, bbPeriod, 0, 2, PRICE_CLOSE);
    if(bbHandle == INVALID_HANDLE || 
       CopyBuffer(bbHandle, 0, 0, 3, bbMiddle) <= 0 ||
       CopyBuffer(bbHandle, 1, 0, 3, bbUpper) <= 0 ||
       CopyBuffer(bbHandle, 2, 0, 3, bbLower) <= 0)
    {
        Print("Error accessing Bollinger Bands");
        return false;
    }

    // Check sideways conditions over 3 bars
    bool isSideway = true;
    for(int i = 0; i < 3; i++)
    {
        bool adxCondition = adx[i] < inputSidewayADXThreshold;
        bool rsiCondition = rsi[i] > 40 && rsi[i] < 60;
        bool bbCondition = (bbUpper[i] - bbLower[i]) / bbMiddle[i] < inputSidewayBBThreshold;
        if(!adxCondition || !rsiCondition || !bbCondition)
        {
            isSideway = false;
            break;
        }
    }

    return isSideway;
}

//+------------------------------------------------------------------+
//| Moving Average trend detection                                   |
//+------------------------------------------------------------------+
bool isUptrend()
{
    double maFast[], maSlow[];
    ArraySetAsSeries(maFast, true);
    ArraySetAsSeries(maSlow, true);
    int maFastHandle = iMA(Symbol(), timeframe, maFastPeriod, 0, MODE_SMA, PRICE_CLOSE);
    int maSlowHandle = iMA(Symbol(), timeframe, maSlowPeriod, 0, MODE_SMA, PRICE_CLOSE);
    if(maFastHandle == INVALID_HANDLE || maSlowHandle == INVALID_HANDLE)
    {
        Print("Error creating MA handles");
        return false;
    }
    if(CopyBuffer(maFastHandle, 0, 0, 3, maFast) <= 0 || CopyBuffer(maSlowHandle, 0, 0, 3, maSlow) <= 0)
    {
        Print("Error copying MA buffers");
        return false;
    }

    // Require MA crossover sustained over 3 bars
    for(int i = 0; i < 3; i++)
    {
        if(maFast[i] <= maSlow[i]) return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| ADX trend strength detection                                     |
//+------------------------------------------------------------------+
bool isStrongTrend()
{
    double adx[], plusDI[], minusDI[];
    if(!getADXValues(adx, plusDI, minusDI, 3)) return false;

    // Require ADX > threshold and DI separation over 3 bars
    for(int i = 0; i < 3; i++)
    {
        if(adx[i] <= inputADXThreshold || MathAbs(plusDI[i] - minusDI[i]) <= inputDISeparation)
            return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| ADX trend direction detection                                    |
//+------------------------------------------------------------------+
bool isUptrendADX()
{
    double adx[], plusDI[], minusDI[];
    if(!getADXValues(adx, plusDI, minusDI, 3)) return false;

    // Require +DI > -DI over 3 bars
    for(int i = 0; i < 3; i++)
    {
        if(plusDI[i] <= minusDI[i]) return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| RSI trend detection (slope-based)                                |
//+------------------------------------------------------------------+
bool isUptrendRSI()
{
    double rsi[];
    ArraySetAsSeries(rsi, true);
    int rsiHandle = iRSI(Symbol(), timeframe, rsiPeriod, PRICE_CLOSE);
    if(rsiHandle == INVALID_HANDLE || CopyBuffer(rsiHandle, 0, 0, 4, rsi) <= 0)
    {
        Print("Error accessing RSI");
        return false;
    }

    // Check RSI slope (increasing over 3 bars) and avoid overbought/oversold
    if(rsi[0] > 70 || rsi[0] < 30) return false; // Avoid extreme RSI
    for(int i = 0; i < 3; i++)
    {
        if(rsi[i] <= rsi[i + 1]) return false; // RSI not increasing
    }
    return true;
}

//+------------------------------------------------------------------+
//| Price action trend detection (higher highs/lower lows)           |
//+------------------------------------------------------------------+
bool isUptrendPriceAction()
{
    double high[], low[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    if(CopyHigh(Symbol(), timeframe, 0, 3, high) <= 0 || CopyLow(Symbol(), timeframe, 0, 3, low) <= 0)
    {
        Print("Error accessing price data");
        return false;
    }

    // Require higher highs and higher lows over 3 bars
    for(int i = 0; i < 2; i++)
    {
        if(high[i] <= high[i + 1] || low[i] <= low[i + 1]) return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| Get ADX values                                                   |
//+------------------------------------------------------------------+
bool getADXValues(double &adx[], double &plusDI[], double &minusDI[], int bars)
{
    ArraySetAsSeries(adx, true);
    ArraySetAsSeries(plusDI, true);
    ArraySetAsSeries(minusDI, true);
    if(adxHandle == INVALID_HANDLE)
    {
        Print("Invalid ADX handle");
        return false;
    }
    if(CopyBuffer(adxHandle, 0, 0, bars, adx) <= 0 ||
       CopyBuffer(adxHandle, 1, 0, bars, plusDI) <= 0 ||
       CopyBuffer(adxHandle, 2, 0, bars, minusDI) <= 0)
    {
        Print("Error copying ADX buffers");
        return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| Trade data structure                                             |
//+------------------------------------------------------------------+
struct KeyValuePair
{
    ulong ticketId;
    string orderType;
    double lot;
    double targetProfit;
    double receiveProfit;
};

KeyValuePair pairs[] = {};

//+------------------------------------------------------------------+
//| Add trade data                                                   |
//+------------------------------------------------------------------+
void addKeyValuePair(ulong ticketId, double lot, double targetProfit, string orderType, double receiveProfit)
{
    int size = ArraySize(pairs);
    ArrayResize(pairs, size + 1);
    pairs[size].lot = lot;
    pairs[size].ticketId = ticketId;
    pairs[size].targetProfit = targetProfit;
    pairs[size].orderType = orderType;
    pairs[size].receiveProfit = receiveProfit;
}

//+------------------------------------------------------------------+
//| Update trade profit                                              |
//+------------------------------------------------------------------+
void updateValuePair(ulong ticketId, double receiveProfit)
{
    for(int i = 0; i < ArraySize(pairs); i++)
    {
        if(pairs[i].ticketId == ticketId)
        {
            pairs[i].receiveProfit = receiveProfit;
            return;
        }
    }
}

//+------------------------------------------------------------------+
//| Reset trade data                                                 |
//+------------------------------------------------------------------+
void resetPairs()
{
    ArrayResize(pairs, 0);
}

//+------------------------------------------------------------------+
//| Print trade data                                                 |
//+------------------------------------------------------------------+
void printPairs()
{
    string output = "\n";
    for(int i = 0; i < ArraySize(pairs); i++)
    {
        output += pairs[i].ticketId + " orderType: " + pairs[i].orderType + 
                 " lot: " + DoubleToString(pairs[i].lot, 2) + 
                 " targetProfit: " + DoubleToString(pairs[i].targetProfit, 2) + 
                 " receiveProfit: " + DoubleToString(pairs[i].receiveProfit, 2);
        if(i < ArraySize(pairs) - 1) output += "\n";
    }
    Print("targetChild: ", output);
}
//+------------------------------------------------------------------+