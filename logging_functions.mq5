//+------------------------------------------------------------------+
//| Logging and Reporting Functions                                 |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Print initialization information                                 |
//+------------------------------------------------------------------+
void PrintInitializationInfo()
{
   Print("=================================================================");
   Print("           ENHANCED TRADING EA v2.0 - INITIALIZATION");
   Print("=================================================================");
   Print("Account Balance: $", DoubleToString(g_AccountStartBalance, 2));
   Print("Daily Profit Target: $", DoubleToString(g_DailyTarget, 2), 
         " (", DoubleToString(DailyProfitTargetPercent, 1), "%)");
   Print("Max Drawdown Allowed: ", DoubleToString(MaxDrawdownPercent, 1), "%");
   Print("Risk Per Trade: ", DoubleToString(RiskPerTradePercent, 1), "%");
   Print("Position Sizing: ", (RiskType == RISK_EQUITY_BASED) ? "Equity-based" : "Fixed lot");
   Print("Initial Lot Size: ", DoubleToString(InitialLotSize, 2));
   Print("Anti-Martingale: ", UseAntiMartingale ? "Enabled" : "Disabled");
   Print("Fibonacci Progression: ", UseFibonacciProgression ? "Enabled" : "Disabled");
   Print("Primary Timeframe: ", EnumToString(PrimaryTimeframe));
   Print("Entry Timeframe: ", EnumToString(EntryTimeframe));
   Print("Trailing Stop: ", EnableTrailingStop ? "Enabled" : "Disabled");
   Print("Partial Profit Taking: ", EnablePartialProfitTaking ? "Enabled" : "Disabled");
   Print("News Filter: ", EnableNewsFilter ? "Enabled" : "Disabled");
   Print("Session Filter: ", EnableSessionFilter ? "Enabled" : "Disabled");
   Print("Magic Number: ", MagicNumber);
   Print("Allowed Symbols: ", AllowedSymbols);
   Print("Current Symbol: ", Symbol(), " - ", IsSymbolAllowed(Symbol()) ? "ALLOWED" : "NOT ALLOWED");
   Print("=================================================================");
   Print("EA initialization completed successfully!");
   Print("=================================================================");
}

//+------------------------------------------------------------------+
//| Print final statistics                                           |
//+------------------------------------------------------------------+
void PrintFinalStats()
{
   Print("=================================================================");
   Print("           ENHANCED TRADING EA v2.0 - FINAL STATISTICS");
   Print("=================================================================");
   Print("Total Trades Today: ", g_TodayStats.totalTrades);
   Print("Winning Trades: ", g_TodayStats.winningTrades);
   Print("Losing Trades: ", g_TodayStats.losingTrades);
   
   if(g_TodayStats.totalTrades > 0)
   {
      double winRate = (double)g_TodayStats.winningTrades / g_TodayStats.totalTrades * 100.0;
      Print("Win Rate: ", DoubleToString(winRate, 1), "%");
   }
   
   Print("Daily Profit: $", DoubleToString(g_TodayStats.profit, 2));
   Print("Daily Target: $", DoubleToString(g_DailyTarget, 2));
   Print("Target Achievement: ", g_DailyTargetReached ? "ACHIEVED" : "NOT ACHIEVED");
   Print("Max Drawdown Reached: ", DoubleToString(g_MaxDrawdownReached, 2), "%");
   Print("Consecutive Wins: ", g_ConsecutiveWins);
   Print("Consecutive Losses: ", g_ConsecutiveLosses);
   
   double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
   double totalReturn = ((currentEquity - g_AccountStartBalance) / g_AccountStartBalance) * 100.0;
   Print("Total Return: ", DoubleToString(totalReturn, 2), "%");
   
   Print("=================================================================");
}

//+------------------------------------------------------------------+
//| Print daily report                                               |
//+------------------------------------------------------------------+
void PrintDailyReport()
{
   Print("=================================================================");
   Print("                    DAILY TRADING REPORT");
   Print("=================================================================");
   Print("Date: ", TimeToString(g_TodayStats.date, TIME_DATE));
   Print("Total Trades: ", g_TodayStats.totalTrades);
   Print("Winning Trades: ", g_TodayStats.winningTrades);
   Print("Losing Trades: ", g_TodayStats.losingTrades);
   
   if(g_TodayStats.totalTrades > 0)
   {
      double winRate = (double)g_TodayStats.winningTrades / g_TodayStats.totalTrades * 100.0;
      Print("Win Rate: ", DoubleToString(winRate, 1), "%");
   }
   
   Print("Daily Profit: $", DoubleToString(g_TodayStats.profit, 2));
   Print("Daily Target: $", DoubleToString(g_DailyTarget, 2));
   Print("Target Achievement: ", g_DailyTargetReached ? "ACHIEVED" : "NOT ACHIEVED");
   Print("Max Drawdown: ", DoubleToString(g_TodayStats.maxDrawdown, 2), "%");
   Print("=================================================================");
}

//+------------------------------------------------------------------+
//| Log trading status                                               |
//+------------------------------------------------------------------+
void LogTradingStatus()
{
   if(!EnableDetailedLogging)
      return;
      
   Print("--- TRADING STATUS ---");
   Print("Time: ", TimeToString(TimeCurrent()));
   Print("Trading Enabled: ", g_TradingEnabled ? "YES" : "NO");
   Print("Daily Target Reached: ", g_DailyTargetReached ? "YES" : "NO");
   Print("Active Positions: ", g_PositionCount);
   Print("Daily Profit: $", DoubleToString(g_DailyProfit, 2));
   Print("Daily Target: $", DoubleToString(g_DailyTarget, 2));
   
   double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
   double currentDrawdown = ((g_AccountStartBalance - currentEquity) / g_AccountStartBalance) * 100.0;
   Print("Current Drawdown: ", DoubleToString(currentDrawdown, 2), "%");
   Print("Max Drawdown Allowed: ", DoubleToString(MaxDrawdownPercent, 2), "%");
   
   Print("Consecutive Wins: ", g_ConsecutiveWins);
   Print("Consecutive Losses: ", g_ConsecutiveLosses);
   
   if(TimeCurrent() < g_TradingPauseUntil)
   {
      Print("Trading Paused Until: ", TimeToString(g_TradingPauseUntil));
   }
   
   // Log current market conditions
   ENUM_TRADE_SIGNAL signal = GetTradeSignal();
   string signalStr = "NONE";
   if(signal == SIGNAL_BUY) signalStr = "BUY";
   else if(signal == SIGNAL_SELL) signalStr = "SELL";
   
   Print("Current Signal: ", signalStr);
   
   // Log indicator values
   double ma_fast[], ma_slow[], rsi[], adx[];
   ArraySetAsSeries(ma_fast, true);
   ArraySetAsSeries(ma_slow, true);
   ArraySetAsSeries(rsi, true);
   ArraySetAsSeries(adx, true);
   
   if(CopyBuffer(g_handleMA_Fast_Primary, 0, 0, 1, ma_fast) > 0 &&
      CopyBuffer(g_handleMA_Slow_Primary, 0, 0, 1, ma_slow) > 0 &&
      CopyBuffer(g_handleRSI_Primary, 0, 0, 1, rsi) > 0 &&
      CopyBuffer(g_handleADX_Primary, 0, 0, 1, adx) > 0)
   {
      Print("MA Fast: ", DoubleToString(ma_fast[0], Digits()));
      Print("MA Slow: ", DoubleToString(ma_slow[0], Digits()));
      Print("RSI: ", DoubleToString(rsi[0], 1));
      Print("ADX: ", DoubleToString(adx[0], 1));
   }
   
   Print("--- END STATUS ---");
}

//+------------------------------------------------------------------+
//| Enhanced error handling                                          |
//+------------------------------------------------------------------+
void HandleTradingError(string operation, int errorCode)
{
   string errorMsg = "";
   
   switch(errorCode)
   {
      case 10004: errorMsg = "Requote"; break;
      case 10006: errorMsg = "Request rejected"; break;
      case 10007: errorMsg = "Request canceled by trader"; break;
      case 10008: errorMsg = "Order placed"; break;
      case 10009: errorMsg = "Request completed"; break;
      case 10010: errorMsg = "Only part of the request was completed"; break;
      case 10011: errorMsg = "Request processing error"; break;
      case 10012: errorMsg = "Request canceled by timeout"; break;
      case 10013: errorMsg = "Invalid request"; break;
      case 10014: errorMsg = "Invalid volume in the request"; break;
      case 10015: errorMsg = "Invalid price in the request"; break;
      case 10016: errorMsg = "Invalid stops in the request"; break;
      case 10017: errorMsg = "Trade is disabled"; break;
      case 10018: errorMsg = "Market is closed"; break;
      case 10019: errorMsg = "There is not enough money to complete the request"; break;
      case 10020: errorMsg = "Prices changed"; break;
      case 10021: errorMsg = "There are no quotes to process the request"; break;
      case 10022: errorMsg = "Invalid order expiration date in the request"; break;
      case 10023: errorMsg = "Order state changed"; break;
      case 10024: errorMsg = "Too frequent requests"; break;
      case 10025: errorMsg = "No changes in request"; break;
      case 10026: errorMsg = "Autotrading disabled by server"; break;
      case 10027: errorMsg = "Autotrading disabled by client"; break;
      case 10028: errorMsg = "Request locked for processing"; break;
      case 10029: errorMsg = "Order or position frozen"; break;
      case 10030: errorMsg = "Invalid order filling type"; break;
      default: errorMsg = "Unknown error"; break;
   }
   
   Print("ERROR in ", operation, ": Code ", errorCode, " - ", errorMsg);
   
   // Handle specific errors
   if(errorCode == 10019) // Not enough money
   {
      Print("CRITICAL: Insufficient funds for trading!");
      g_TradingEnabled = false;
   }
   else if(errorCode == 10017 || errorCode == 10026 || errorCode == 10027) // Trading disabled
   {
      Print("CRITICAL: Trading is disabled!");
      g_TradingEnabled = false;
   }
   else if(errorCode == 10024) // Too frequent requests
   {
      Print("WARNING: Too frequent requests - pausing for 30 seconds");
      g_TradingPauseUntil = TimeCurrent() + 30;
   }
}

//+------------------------------------------------------------------+
//| Validate trading environment                                     |
//+------------------------------------------------------------------+
bool ValidateTradingEnvironment()
{
   // Check if autotrading is enabled
   if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
   {
      Print("ERROR: Autotrading is disabled in terminal");
      return false;
   }
   
   // Check if EA trading is allowed
   if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
   {
      Print("ERROR: EA trading is not allowed");
      return false;
   }
   
   // Check account type
   ENUM_ACCOUNT_TRADE_MODE tradeMode = (ENUM_ACCOUNT_TRADE_MODE)AccountInfoInteger(ACCOUNT_TRADE_MODE);
   if(tradeMode != ACCOUNT_TRADE_MODE_REAL && tradeMode != ACCOUNT_TRADE_MODE_DEMO)
   {
      Print("WARNING: Unknown account trade mode");
   }
   
   // Check symbol properties
   if(!SymbolInfoInteger(Symbol(), SYMBOL_TRADE_MODE))
   {
      Print("ERROR: Trading is disabled for symbol ", Symbol());
      return false;
   }
   
   return true;
}
