//+------------------------------------------------------------------+
//|                                    anti-martingale-fibonacci.mq5 |
//|                                  Copyright 2023, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link "https://www.mql5.com"
#property version "1.10" // Updated version after improvements

#include <Trade\Trade.mqh>

//--- input parameters
input bool autoCheckTrend = false;
input bool isSkipBonus = false;
input string manualStartTrend = "BUY";
input double initAccountBalance = 4000;
input double takeProfit = 0.005;
input double targetDay = 400;
input double initialLot = 0.05;
input double maxLot = 1.0; // Maximum lot size for risk management

int  FiboSeq[11] = {1,1,2,3,5,8,13,21,34,55,89};

bool g_isBuyTrend = manualStartTrend == "BUY";
bool g_isFinishing = false;

double g_priceArray[];
int g_arraySize = 0;

double g_targetProfitArray[];
int g_arraySizeTargetProfit = 0;

double g_targetDay = targetDay;
double g_collectTargetDay = 0;

double g_targetProfit = 0;
double g_accountBalance = 0;

datetime g_lastPrintTime = 0;
datetime g_bonusPrintTime = 0;

ulong currentTicketId = 0;
int count = 1;

bool startBonus = false;
bool startBonusDone = false;
bool skipBonus = isSkipBonus;
ulong currentBonusTicketId = 0;
datetime timeBonus = 0;
int adxHandle = INVALID_HANDLE;

CTrade Trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
  g_accountBalance = initAccountBalance == 0 ? AccountInfoDouble(ACCOUNT_BALANCE) : initAccountBalance;
  g_targetProfit = g_accountBalance * takeProfit;

  Print("******************************START_INIT******************************");
  Print("Target day: ", g_targetDay);
  Print("Độ lớn của lệnh đầu tiên: ", initialLot);
  Print("Maximum lot size: ", maxLot);
  Print("Vốn hiện có: ", initAccountBalance);
  Print("Lợi nhuận để đóng tất cả lệnh: ", g_targetProfit);
  Print("******************************END_INIT******************************");

  // Initialize ADX handle
  adxHandle = iADX(Symbol(), PERIOD_CURRENT, 14);
  if(adxHandle == INVALID_HANDLE)
  {
    Print("Error creating ADX indicator handle");
    return(INIT_FAILED);
  }

  Trade.SetDeviationInPoints(10);
  Trade.SetTypeFilling(ORDER_FILLING_FOK);
  Trade.SetAsyncMode(false);

  return (INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
  if (g_collectTargetDay >= g_targetDay || g_collectTargetDay <= -g_targetDay*2)
  {
    if (!g_isFinishing)
    {
      g_isFinishing = true;
      Print("***************DONE_TARGET_DAY***************");
      Print("Current Target Day: ", g_collectTargetDay);
      printPairs();
      Print("***************DONE_TARGET_DAY***************");
    }
    return;
  }
  checkAndOpenAdditionalOrders();
}

void OnDeinit(const int reason)
{
  if (adxHandle != INVALID_HANDLE)
  {
    IndicatorRelease(adxHandle);
  }
}

//+------------------------------------------------------------------+
//| Mở lệnh                                                 |
//+------------------------------------------------------------------+
bool OpenOrder()
{
   int trend = checkTrend();
   string currentTrend = "NONE";
   bool isBuyTrend = true;
   switch (trend)
   {
    case 1:
      isBuyTrend = true;
      currentTrend = "UP";
      break;
    case -1:
      isBuyTrend = false;
      currentTrend = "DOWN";
      break;
    case 0:
      isBuyTrend = manualStartTrend == "BUY";
      currentTrend = isBuyTrend ? "UP" : "DOWN";
      break;
  }
  double currentPrice = SymbolInfoDouble(Symbol(), isBuyTrend ? SYMBOL_ASK : SYMBOL_BID);
  // Calculate lot size with Fibonacci sequence but cap at maximum lot size
  double newLots = MathMin(initialLot * FiboSeq[count], maxLot);

  // Log the lot size calculation for monitoring
  Print("Calculated lot size: ", initialLot * FiboSeq[count], " | Applied lot size: ", newLots);

  isBuyTrend
      ? Trade.Buy(newLots, Symbol(), 0, 0, 0, "Open BUY Order")
      : Trade.Sell(newLots, Symbol(), 0, 0, 0, "Open SELL Order");
  currentTicketId = Trade.ResultOrder();

  if (currentTicketId > 0)
  {
    // Store the actual lot size used, not just initialLot
    addKeyValuePair(currentTicketId, newLots, g_targetProfit * FiboSeq[count], isBuyTrend ? "BUY" : "SELL", 0.0);
    return true;
  }
  return false;
}

//+------------------------------------------------------------------+
//| Kiểm tra và mở thêm lệnh                                         |
//+------------------------------------------------------------------+
void checkAndOpenAdditionalOrders()
{
   if (currentTicketId == 0 || !PositionSelectByTicket(currentTicketId))
   {
      OpenOrder();
      return;
   } 
   
    double profit = PositionGetDouble(POSITION_PROFIT);
    double targetProfit = g_targetProfit * FiboSeq[count];
    double targetSL = -g_targetProfit * FiboSeq[count-1]; // Modified to be proportional to current position size but with reduced risk
   
    // *********************** Bonus ***********************
    if (startBonus)
    {
      startBonusDone = true;
   
      datetime currentTime = TimeCurrent();
      if (currentTime - timeBonus >= 100)
      {
        startBonus = false;
      }
   
      // More conservative bonus target with dynamic adjustment based on market volatility
      double atr[];
      ArraySetAsSeries(atr, true);
      int atrHandle = iATR(Symbol(), PERIOD_CURRENT, 14);
      CopyBuffer(atrHandle, 0, 0, 1, atr);
   
      // If market is more volatile, use a more conservative target
      double volatilityFactor = MathMin(1.5, MathMax(1.2, 1.5 - (atr[0] * 100)));
      double targetBonusProfit = targetProfit * volatilityFactor;
   
      Print("Volatility factor: ", volatilityFactor, " | ATR: ", atr[0]);
   
      if (currentTime - g_bonusPrintTime >= 15)
      {
        Print("***************START_BONUS***************");
        Print("Profit của vị thế: ", profit);
        Print("Target Profit: ", targetBonusProfit);
        Print("Dương: " + (profit >= targetBonusProfit ? "true" : "false"));
        Print("***************END_BONUS***************");
        g_bonusPrintTime = currentTime;
      }
   
      if (profit >= targetBonusProfit)
      {
        Trade.PositionClose(currentTicketId);
   
        g_collectTargetDay += profit;
        updateValuePair(currentTicketId, profit);
        count += 1;
        currentTicketId = 0;
        startBonusDone = false;
      }
   
      return;
    }
    // *********************** End bonus ***********************
   
    // *********************** Print profit ***********************
    datetime currentTime = TimeCurrent();
    if (currentTime - g_lastPrintTime >= 20)
    {
      Print("***************START_DONE***************");
      Print("Profit của vị thế: ", profit);
      Print("Target Profit: ", targetProfit);
      Print("Dương: " + (profit >= targetProfit ? "true" : "false") + " || Âm: " + (profit <= -targetProfit ? "true" : "false"));
      Print("Target Day: ", g_targetDay);
      Print("Current Target Day: ", g_collectTargetDay);
      printPairs();
      Print("***************END_DONE***************");
      g_lastPrintTime = currentTime;
    }
    // *********************** End print profit ***********************
   
    if (profit >= targetProfit)
    {
      // *********************** Bonus ***********************
      if (!skipBonus && !startBonus && !startBonusDone)
      {
        startBonus = true;
        timeBonus = TimeCurrent();
        return;
      }
      // *********************** End bonus ***********************
   
      Trade.PositionClose(currentTicketId);
   
      g_collectTargetDay += profit;
      updateValuePair(currentTicketId, profit);
      count += 1;
      currentTicketId = 0;
      startBonusDone = false;
      return;
    }
   
    if (profit <= targetSL)
    {
      g_collectTargetDay += profit;
      Trade.PositionClose(currentTicketId);
      updateValuePair(currentTicketId, profit);
      count = 1;
      startBonusDone = false;
    }
}

//+------------------------------------------------------------------+
//| Check trend                                                       |
//+------------------------------------------------------------------+
int checkTrend()
{
  // Thêm kiểm tra sideway
  if (isSideway())
  {
    int direction = getSidewayDirection();
    switch (direction)
    {
    case 1:
      Print("***************SIDEWAY_UP***************");
      break;
    case -1:
      Print("***************SIDEWAY_DOWN***************");
      break;
    case 0:
      Print("***************SIDEWAY_NONE***************");
      break;
    }

    return direction;
  }

  bool maUptrend = isUptrend();
  bool adxStrong = isStrongTrend();
  bool adxUptrend = isUptrendADX();
  bool rsiUptrend = isUptrendRSI();

  // Đếm số điều kiện trend tăng
  int uptrendCount = 0;
  if (maUptrend)
    uptrendCount++;
  if (adxStrong && adxUptrend)
    uptrendCount++;
  if (rsiUptrend)
    uptrendCount++;

  // Đếm số điều kiện trend giảm
  int downtrendCount = 0;
  if (!maUptrend)
    downtrendCount++;
  if (adxStrong && !adxUptrend)
    downtrendCount++;
  if (!rsiUptrend)
    downtrendCount++;

  // Trả về kết quả
  if (uptrendCount >= 2)
  {
    Print("***************Trend_UP***************");
    return 1;
  } // Trend tăng

  if (downtrendCount >= 2)
  {
    Print("***************Trend_DOWN***************");
    return -1; // Trend giảm
  }

  return 0; // Không có trend rõ ràng
}

int getSidewayDirection()
{
  // Kiểm tra RSI
  double rsi[];
  ArraySetAsSeries(rsi, true);
  int rsiHandle = iRSI(Symbol(), PERIOD_CURRENT, 14, PRICE_CLOSE);
  CopyBuffer(rsiHandle, 0, 0, 1, rsi);

  // Kiểm tra Bollinger Bands
  double bbUpper[], bbMiddle[], bbLower[];
  ArraySetAsSeries(bbUpper, true);
  ArraySetAsSeries(bbMiddle, true);
  ArraySetAsSeries(bbLower, true);

  int bbHandle = iBands(Symbol(), PERIOD_CURRENT, 20, 0, 2, PRICE_CLOSE);
  if (bbHandle == INVALID_HANDLE)
    return 0;

  if (CopyBuffer(bbHandle, 0, 0, 1, bbMiddle) <= 0 ||
      CopyBuffer(bbHandle, 1, 0, 1, bbUpper) <= 0 ||
      CopyBuffer(bbHandle, 2, 0, 1, bbLower) <= 0)
    return 0;

  // Lấy giá hiện tại
  double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);

  // Kiểm tra vị trí giá so với BB
  bool priceAboveMiddle = currentPrice > bbMiddle[0];
  bool priceNearUpper = currentPrice > (bbUpper[0] - (bbUpper[0] - bbMiddle[0]) * 0.3);
  bool priceNearLower = currentPrice < (bbLower[0] + (bbMiddle[0] - bbLower[0]) * 0.3);

  // Kiểm tra RSI
  bool rsiAbove50 = rsi[0] > 50;
  bool rsiBelow50 = rsi[0] < 50;

  // Đếm số điều kiện tăng
  int uptrendCount = 0;
  if (priceAboveMiddle)
    uptrendCount++;
  if (priceNearUpper)
    uptrendCount++;
  if (rsiAbove50)
    uptrendCount++;

  // Đếm số điều kiện giảm
  int downtrendCount = 0;
  if (!priceAboveMiddle)
    downtrendCount++;
  if (priceNearLower)
    downtrendCount++;
  if (rsiBelow50)
    downtrendCount++;

  // Trả về kết quả
  if (uptrendCount >= 2)
    return 1; // Xu hướng tăng trong sideway
  if (downtrendCount >= 2)
    return -1; // Xu hướng giảm trong sideway
  return 0;    // Không có xu hướng rõ ràng
}

bool isSideway()
{
  // Kiểm tra ADX
  double adx[], plusDI[], minusDI[];
  if (!getADXValues(adx, plusDI, minusDI))
    return false;

  // Kiểm tra RSI
  double rsi[];
  ArraySetAsSeries(rsi, true);
  int rsiHandle = iRSI(Symbol(), PERIOD_CURRENT, 14, PRICE_CLOSE);
  CopyBuffer(rsiHandle, 0, 0, 1, rsi);

  // Kiểm tra Bollinger Bands
  double bbUpper[], bbMiddle[], bbLower[];
  ArraySetAsSeries(bbUpper, true);
  ArraySetAsSeries(bbMiddle, true);
  ArraySetAsSeries(bbLower, true);

  int bbHandle = iBands(Symbol(), PERIOD_CURRENT, 20, 0, 2, PRICE_CLOSE);
  if (bbHandle == INVALID_HANDLE)
    return false;

  if (CopyBuffer(bbHandle, 0, 0, 1, bbMiddle) <= 0 ||
      CopyBuffer(bbHandle, 1, 0, 1, bbUpper) <= 0 ||
      CopyBuffer(bbHandle, 2, 0, 1, bbLower) <= 0)
    return false;

  // Điều kiện sideway:
  // 1. ADX < 25 (thị trường yếu hoặc không có xu hướng rõ ràng)
  // 2. RSI gần 50 (không có xu hướng rõ ràng)
  // 3. Khoảng cách BB hẹp (biến động thấp)
  bool adxCondition = adx[0] < 25;
  bool rsiCondition = rsi[0] > 40 && rsi[0] < 60; // Mở rộng phạm vi để nhận diện sideway tốt hơn
  bool bbCondition = (bbUpper[0] - bbLower[0]) / bbMiddle[0] < 0.025; // 2.5% biến động

  return adxCondition && rsiCondition && bbCondition;
}

bool getMAValues(int period, double &ma[])
{
  ArraySetAsSeries(ma, true);

  int maHandle = iMA(Symbol(), PERIOD_CURRENT, period, 0, MODE_SMA, PRICE_CLOSE);

  if (maHandle == INVALID_HANDLE)
  {
    Print("Lỗi tạo MA handle");
    return false;
  }

  if (CopyBuffer(maHandle, 0, 0, 1, ma) <= 0)
  {
    Print("Lỗi copy MA buffer");
    return false;
  }

  return true;
}

bool isUptrend()
{
  double ma20[], ma50[];
  if (!getMAValues(20, ma20) || !getMAValues(50, ma50))
    return false;

  return ma20[0] > ma50[0];
}

bool isDowntrend()
{
  double ma20[], ma50[];
  if (!getMAValues(20, ma20) || !getMAValues(50, ma50))
    return false;

  return ma20[0] < ma50[0];
}

bool isStrongTrend()
{
  double adx[], plusDI[], minusDI[];
  if (!getADXValues(adx, plusDI, minusDI))
    return false;

  // Stronger trend detection with additional condition for DI separation
  return adx[0] > 25 && MathAbs(plusDI[0] - minusDI[0]) > 5;
}

bool isUptrendADX()
{
  double adx[], plusDI[], minusDI[];
  if (!getADXValues(adx, plusDI, minusDI))
    return false;

  return plusDI[0] > minusDI[0];
}

bool isUptrendHHLL()
{
  double high1 = iHigh(Symbol(), PERIOD_CURRENT, 1);
  double high2 = iHigh(Symbol(), PERIOD_CURRENT, 2);
  double low1 = iLow(Symbol(), PERIOD_CURRENT, 1);
  double low2 = iLow(Symbol(), PERIOD_CURRENT, 2);

  return (high1 > high2) && (low1 > low2);
}

bool isDowntrendHHLL()
{
  double high1 = iHigh(Symbol(), PERIOD_CURRENT, 1);
  double high2 = iHigh(Symbol(), PERIOD_CURRENT, 2);
  double low1 = iLow(Symbol(), PERIOD_CURRENT, 1);
  double low2 = iLow(Symbol(), PERIOD_CURRENT, 2);

  return (high1 < high2) && (low1 < low2);
}

bool isUptrendRSI()
{
  double rsi[];
  ArraySetAsSeries(rsi, true);
  int rsiHandle = iRSI(Symbol(), PERIOD_CURRENT, 14, PRICE_CLOSE);
  CopyBuffer(rsiHandle, 0, 0, 1, rsi);

  return rsi[0] > 50; // RSI > 50 thường là trend tăng
}

bool isDowntrendRSI()
{
  double rsi[];
  ArraySetAsSeries(rsi, true);
  int rsiHandle = iRSI(Symbol(), PERIOD_CURRENT, 14, PRICE_CLOSE);
  CopyBuffer(rsiHandle, 0, 0, 1, rsi);

  return rsi[0] < 50; // RSI < 50 thường là trend giảm
}

bool getADXValues(double &adx[], double &plusDI[], double &minusDI[])
{
  ArraySetAsSeries(adx, true);
  ArraySetAsSeries(plusDI, true);
  ArraySetAsSeries(minusDI, true);

  int adxHandle = iADX(Symbol(), PERIOD_CURRENT, 14);

  if (adxHandle == INVALID_HANDLE)
  {
    Print("Lỗi tạo ADX handle");
    return false;
  }

  if (CopyBuffer(adxHandle, 0, 0, 1, adx) <= 0 ||
      CopyBuffer(adxHandle, 1, 0, 1, plusDI) <= 0 ||
      CopyBuffer(adxHandle, 2, 0, 1, minusDI) <= 0)
  {
    Print("Lỗi copy ADX buffers");
    return false;
  }

  return true;
}

//+------------------------------------------------------------------+
//| Object                                                            |
//+------------------------------------------------------------------+
struct KeyValuePair
{
  ulong ticketId;
  string orderType;
  double lot;
  double targetProfit;
  double receiveProfit;
};

KeyValuePair pairs[] = {};

KeyValuePair getPairValueByTicketIt(ulong ticketId)
{
  for (int i = 0; i < ArraySize(pairs); i++)
  {
    if (pairs[i].ticketId == ticketId)
      return pairs[i];
  }

  KeyValuePair defaultPair;
  defaultPair.ticketId = 0;
  defaultPair.lot = 0.0;
  defaultPair.orderType = "NONE";
  defaultPair.targetProfit = 0.0;
  defaultPair.receiveProfit = 0.0;
  return defaultPair;
}

void addKeyValuePair(ulong ticketId, double lot, double targetProfit, string orderType, double receiveProfit)
{
  int size = ArraySize(pairs);
  ArrayResize(pairs, size + 1);
  pairs[size].lot = lot;
  pairs[size].ticketId = ticketId;
  pairs[size].targetProfit = targetProfit;
  pairs[size].orderType = orderType;
  pairs[size].receiveProfit = receiveProfit;
}

void updateValuePair(ulong ticketId, double receiveProfit)
{
  for (int i = 0; i < ArraySize(pairs); i++)
  {
    if (pairs[i].ticketId == ticketId)
    {
      pairs[i].receiveProfit = receiveProfit;
      return;
    }
  }
}

void removeKeyValuePair(ulong ticketId)
{
  int size = ArraySize(pairs);
  for (int i = 0; i < size; i++)
  {
    if (pairs[i].ticketId == ticketId)
    {
      for (int j = i; j < size - 1; j++)
      {
        pairs[j] = pairs[j + 1];
      }
      ArrayResize(pairs, size - 1);
      return;
    }
  }
}

void resetPairs()
{
  ArrayResize(pairs, 0);
}

void printPairs()
{
  string output = "\n";
  for (int i = 0; i < ArraySize(pairs); i++)
  {
    output += pairs[i].ticketId + " orderType: " + pairs[i].orderType + " lot: " + DoubleToString(pairs[i].lot, 2) + " targetProfit: " + DoubleToString(pairs[i].targetProfit, 2) + " receiveProfit: " + DoubleToString(pairs[i].receiveProfit, 2);
    if (i < ArraySize(pairs) - 1)
      output += "\n";
  }
  Print("targetChild: ", output);
}
//+------------------------------------------------------------------+
//| End object                                                        |
//+------------------------------------------------------------------+