//+------------------------------------------------------------------+
//|                                           Enhanced Trading Bot v2.0 |
//|                                  Copyright 2025, Enhanced Trading Systems |
//|                                    Advanced MT5 EA for Passive Income |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Enhanced Trading Systems"
#property link "https://www.enhancedtrading.com"
#property version "2.00"
#property description "Advanced Multi-Timeframe Trading EA with Anti-Martingale Risk Management"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//=================================================================
//                    ENUMERATIONS
//=================================================================

enum ENUM_RISK_TYPE
{
   RISK_FIXED_LOT,      // Fixed lot size
   RISK_EQUITY_BASED    // Equity-based sizing
};

enum ENUM_TRADE_SIGNAL
{
   SIGNAL_NONE,         // No signal
   SIGNAL_BUY,          // Buy signal
   SIGNAL_SELL          // Sell signal
};

//=================================================================
//                    INPUT PARAMETERS
//=================================================================

//--- Risk Management Settings
input group "=== RISK MANAGEMENT ==="
input ENUM_RISK_TYPE RiskType = RISK_EQUITY_BASED;           // Position sizing method
input double MaxDailyLossPercent = 15.0;                     // Maximum daily loss % (15%)
input double RiskPerTradePercent = 2.0;                      // Risk per trade % of equity
input double InitialLotSize = 0.05;                          // Initial lot size (if fixed)
input int MaxConsecutiveLosses = 5;                          // Max consecutive losses before pause
input bool UseAntiMartingale = true;                         // Use Anti-Martingale progression
input bool UseFibonacciProgression = true;                   // Use Fibonacci sequence for sizing

//--- Trading Strategy Settings
input group "=== TRADING STRATEGY ==="
input bool AutoTrendDetection = true;                        // Enable automatic trend detection
input ENUM_TIMEFRAMES PrimaryTimeframe = PERIOD_M5;          // Primary timeframe for trend
input ENUM_TIMEFRAMES EntryTimeframe = PERIOD_M1;            // Entry timeframe for signals
input double DailyProfitTargetPercent = 10.0;                // Daily profit target % of balance
input bool EnableTrailingStop = true;                        // Enable trailing stop
input bool EnablePartialProfitTaking = true;                 // Enable partial profit taking
input double PartialClosePercent = 50.0;                     // % of position to close at first target

//--- Trade Duration Management
input group "=== TRADE DURATION CONTROL ==="
input bool EnableTradeDurationControl = true;                // Enable trade duration monitoring
input int MaxTradeMinutes = 240;                             // Maximum trade duration in minutes (4 hours)
input int ScalpingMaxMinutes = 30;                           // Max duration for scalping trades (30 min)
input int TrendMaxMinutes = 120;                             // Max duration for trend trades (2 hours)
input bool UseAdaptiveDuration = true;                       // Use adaptive duration based on market conditions
input bool ForceCloseAtDuration = true;                      // Force close trades at max duration

//--- Trailing Stop Optimization
input group "=== TRAILING STOP OPTIMIZATION ==="
input int TrailingStopThrottleSeconds = 30;                  // Minimum seconds between trailing stop updates
input double MinTrailingStepPoints = 5.0;                    // Minimum points to move trailing stop
input bool EnableSmartTrailing = true;                       // Enable intelligent trailing stop logic

//--- Trade Frequency Optimization
input group "=== TRADE FREQUENCY OPTIMIZATION ==="
input int MinSecondsBetweenTrades = 60;                      // Minimum seconds between new trades
input bool EnableFrequencyControl = true;                    // Enable trade frequency control
input double DailyTargetProgressiveIncrease = 1.1;           // Increase daily target by 10% after reaching it

//--- Technical Analysis Settings
input group "=== TECHNICAL ANALYSIS ==="
input int MA_Fast_Period = 20;                               // Fast MA period
input int MA_Slow_Period = 50;                               // Slow MA period
input int ADX_Period = 14;                                   // ADX period
input double ADX_Threshold = 25.0;                           // ADX strength threshold
input int RSI_Period = 14;                                   // RSI period
input double RSI_Overbought = 70.0;                          // RSI overbought level
input double RSI_Oversold = 30.0;                            // RSI oversold level
input int ATR_Period = 14;                                   // ATR period for stops

//--- News and Session Filters
input group "=== FILTERS ==="
input bool EnableNewsFilter = false;                         // Enable news filter
input bool EnableSessionFilter = false;                      // Enable trading session filter
input int NewsFilterMinutes = 30;                            // Minutes to avoid before/after news
input string TradingStartTime = "00:00";                     // Trading start time
input string TradingEndTime = "23:59";                       // Trading end time

//--- Advanced Settings
input group "=== ADVANCED ==="
input string AllowedSymbols = "XAUUSD,BTCUSD,EURUSD";       // Allowed trading symbols (comma separated)
input int MagicNumber = 123456;                              // Magic number for orders
input string TradeComment = "Enhanced_EA_v2.0";              // Trade comment
input bool EnableDetailedLogging = true;                     // Enable detailed logging



//=================================================================
//                    GLOBAL VARIABLES
//=================================================================

// Trading objects
CTrade Trade;
CPositionInfo PositionInfo;
COrderInfo OrderInfo;

// Indicator handles
int g_handleMA_Fast_Primary = INVALID_HANDLE;
int g_handleMA_Slow_Primary = INVALID_HANDLE;
int g_handleMA_Fast_Entry = INVALID_HANDLE;
int g_handleMA_Slow_Entry = INVALID_HANDLE;
int g_handleADX_Primary = INVALID_HANDLE;
int g_handleADX_Entry = INVALID_HANDLE;
int g_handleRSI_Primary = INVALID_HANDLE;
int g_handleRSI_Entry = INVALID_HANDLE;
int g_handleATR = INVALID_HANDLE;

// Trading state variables
bool g_TradingEnabled = true;
bool g_DailyTargetReached = false;
datetime g_LastTradeTime = 0;
datetime g_TradingPauseUntil = 0;
int g_ConsecutiveLosses = 0;
int g_ConsecutiveWins = 0;
double g_DailyProfit = 0;
double g_DailyTarget = 0;
double g_AccountStartBalance = 0;
double g_MaxDrawdownReached = 0;

// Fibonacci sequence for position sizing
int g_FibonacciSequence[] = {1, 1, 2, 3, 5, 8, 13, 21, 34, 55};
int g_CurrentFibIndex = 0;

// Position management
struct PositionData
{
   ulong ticket;
   double openPrice;
   double lotSize;
   double initialSL;
   double initialTP;
   bool partialClosed;
   datetime openTime;
   ENUM_POSITION_TYPE type;
   int maxDurationMinutes;        // Maximum duration for this specific trade
   bool isScalpingTrade;          // Flag to identify scalping vs trend trades
   double entryVolatility;        // ATR at entry time for adaptive duration
   bool durationWarningIssued;    // Flag to track if duration warning was issued
   datetime lastTrailingUpdate;   // Last time trailing stop was updated
   double lastTrailingPrice;      // Last price where trailing stop was set
};

PositionData g_CurrentPositions[];
int g_PositionCount = 0;

// Daily statistics
struct DailyStats
{
   datetime date;
   double profit;
   int totalTrades;
   int winningTrades;
   int losingTrades;
   double maxDrawdown;
};

DailyStats g_TodayStats;

// Symbol validation
string g_AllowedSymbolsArray[];
int g_AllowedSymbolsCount = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Initialize trading objects
   Trade.SetExpertMagicNumber(MagicNumber);
   Trade.SetDeviationInPoints(10);
   Trade.SetTypeFilling(ORDER_FILLING_FOK);
   Trade.SetAsyncMode(false);

   // Initialize account data
   g_AccountStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   g_DailyTarget = g_AccountStartBalance * (DailyProfitTargetPercent / 100.0);

   // Initialize daily stats
   ResetDailyStats();

   // Parse allowed symbols
   if(!ParseAllowedSymbols())
   {
      Print("ERROR: Failed to parse allowed symbols");
      return INIT_FAILED;
   }

   // Validate current symbol
   if(!IsSymbolAllowed(Symbol()))
   {
      Print("ERROR: Current symbol ", Symbol(), " is not in allowed symbols list");
      return INIT_FAILED;
   }

   // Initialize indicators
   if(!InitializeIndicators())
   {
      Print("ERROR: Failed to initialize indicators");
      return INIT_FAILED;
   }

   // Initialize position arrays
   ArrayResize(g_CurrentPositions, 0);
   g_PositionCount = 0;

   // Print initialization info
   PrintInitializationInfo();

   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Release indicator handles
   ReleaseIndicators();

   // Print final statistics
   PrintFinalStats();

   Print("EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check if new day started
   CheckNewDay();

   // Check if trading is enabled
   if(!g_TradingEnabled || g_DailyTargetReached)
   {
      ManageExistingPositions();
      return;
   }

   // Check trading pause
   if(TimeCurrent() < g_TradingPauseUntil)
   {
      if(EnableDetailedLogging)
         Print("Trading paused until: ", TimeToString(g_TradingPauseUntil));
      return;
   }

   // Check daily loss protection
   if(!CheckDailyLossProtection())
   {
      return;
   }

   // Check session filter
   if(EnableSessionFilter && !IsWithinTradingSession())
   {
      return;
   }

   // Check news filter
   if(EnableNewsFilter && IsNewsTime())
   {
      return;
   }

   // Update daily statistics
   UpdateDailyStats();

   // Manage existing positions
   ManageExistingPositions();

   // Check for new trading opportunities
   if(CanOpenNewPosition())
   {
      ENUM_TRADE_SIGNAL signal = GetTradeSignal();

      // Signal is always BUY or SELL (never NONE)
      if(signal == SIGNAL_BUY)
      {
         OpenBuyPosition();
      }
      else if(signal == SIGNAL_SELL)
      {
         OpenSellPosition();
      }
   }

   // Log status periodically
   static datetime lastLogTime = 0;
   if(TimeCurrent() - lastLogTime >= 300) // Every 5 minutes
   {
      LogTradingStatus();
      lastLogTime = TimeCurrent();
   }
}

//=================================================================
//                    CORE TRADING FUNCTIONS
//=================================================================

//+------------------------------------------------------------------+
//| Initialize all indicators                                        |
//+------------------------------------------------------------------+
bool InitializeIndicators()
{
   // Primary timeframe indicators
   g_handleMA_Fast_Primary = iMA(Symbol(), PrimaryTimeframe, MA_Fast_Period, 0, MODE_SMA, PRICE_CLOSE);
   g_handleMA_Slow_Primary = iMA(Symbol(), PrimaryTimeframe, MA_Slow_Period, 0, MODE_SMA, PRICE_CLOSE);
   g_handleADX_Primary = iADX(Symbol(), PrimaryTimeframe, ADX_Period);
   g_handleRSI_Primary = iRSI(Symbol(), PrimaryTimeframe, RSI_Period, PRICE_CLOSE);

   // Entry timeframe indicators
   g_handleMA_Fast_Entry = iMA(Symbol(), EntryTimeframe, MA_Fast_Period, 0, MODE_SMA, PRICE_CLOSE);
   g_handleMA_Slow_Entry = iMA(Symbol(), EntryTimeframe, MA_Slow_Period, 0, MODE_SMA, PRICE_CLOSE);
   g_handleADX_Entry = iADX(Symbol(), EntryTimeframe, ADX_Period);
   g_handleRSI_Entry = iRSI(Symbol(), EntryTimeframe, RSI_Period, PRICE_CLOSE);

   // ATR for stop loss calculation
   g_handleATR = iATR(Symbol(), PrimaryTimeframe, ATR_Period);

   // Check if all handles are valid
   if(g_handleMA_Fast_Primary == INVALID_HANDLE || g_handleMA_Slow_Primary == INVALID_HANDLE ||
      g_handleADX_Primary == INVALID_HANDLE || g_handleRSI_Primary == INVALID_HANDLE ||
      g_handleMA_Fast_Entry == INVALID_HANDLE || g_handleMA_Slow_Entry == INVALID_HANDLE ||
      g_handleADX_Entry == INVALID_HANDLE || g_handleRSI_Entry == INVALID_HANDLE ||
      g_handleATR == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create indicator handles");
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Release indicator handles                                        |
//+------------------------------------------------------------------+
void ReleaseIndicators()
{
   if(g_handleMA_Fast_Primary != INVALID_HANDLE) IndicatorRelease(g_handleMA_Fast_Primary);
   if(g_handleMA_Slow_Primary != INVALID_HANDLE) IndicatorRelease(g_handleMA_Slow_Primary);
   if(g_handleMA_Fast_Entry != INVALID_HANDLE) IndicatorRelease(g_handleMA_Fast_Entry);
   if(g_handleMA_Slow_Entry != INVALID_HANDLE) IndicatorRelease(g_handleMA_Slow_Entry);
   if(g_handleADX_Primary != INVALID_HANDLE) IndicatorRelease(g_handleADX_Primary);
   if(g_handleADX_Entry != INVALID_HANDLE) IndicatorRelease(g_handleADX_Entry);
   if(g_handleRSI_Primary != INVALID_HANDLE) IndicatorRelease(g_handleRSI_Primary);
   if(g_handleRSI_Entry != INVALID_HANDLE) IndicatorRelease(g_handleRSI_Entry);
   if(g_handleATR != INVALID_HANDLE) IndicatorRelease(g_handleATR);
}

//+------------------------------------------------------------------+
//| Get trade signal - Enhanced with multiple strategies            |
//+------------------------------------------------------------------+
ENUM_TRADE_SIGNAL GetTradeSignal()
{
   // Strategy 1: Multi-timeframe trend analysis
   int primaryTrend = GetTrendDirection(PrimaryTimeframe);
   int entrySignal = GetEntrySignal(EntryTimeframe);

   // Strategy 2: Price action analysis
   int priceActionSignal = GetPriceActionSignal();

   // Strategy 3: Momentum analysis
   int momentumSignal = GetMomentumSignal();

   // Strategy 4: Mean reversion analysis
   int meanReversionSignal = GetMeanReversionSignal();

   // Strategy 5: Volume-based analysis
   int volumeSignal = GetVolumeSignal();

   // Collect all signals
   int buySignals = 0;
   int sellSignals = 0;

   // Count trend signals
   if(primaryTrend == 1) buySignals++;
   else if(primaryTrend == -1) sellSignals++;

   if(entrySignal == 1) buySignals++;
   else if(entrySignal == -1) sellSignals++;

   // Count price action signals
   if(priceActionSignal == 1) buySignals++;
   else if(priceActionSignal == -1) sellSignals++;

   // Count momentum signals
   if(momentumSignal == 1) buySignals++;
   else if(momentumSignal == -1) sellSignals++;

   // Count mean reversion signals
   if(meanReversionSignal == 1) buySignals++;
   else if(meanReversionSignal == -1) sellSignals++;

   // Count volume signals
   if(volumeSignal == 1) buySignals++;
   else if(volumeSignal == -1) sellSignals++;

   // Decision logic - always return BUY or SELL
   if(buySignals > sellSignals)
   {
      return SIGNAL_BUY;
   }
   else if(sellSignals > buySignals)
   {
      return SIGNAL_SELL;
   }
   else
   {
      // Tie-breaker: Use current price position relative to MA
      double ma_fast[], ma_slow[];
      ArraySetAsSeries(ma_fast, true);
      ArraySetAsSeries(ma_slow, true);

      if(CopyBuffer(g_handleMA_Fast_Primary, 0, 0, 1, ma_fast) > 0 &&
         CopyBuffer(g_handleMA_Slow_Primary, 0, 0, 1, ma_slow) > 0)
      {
         double currentPrice = (SymbolInfoDouble(Symbol(), SYMBOL_ASK) + SymbolInfoDouble(Symbol(), SYMBOL_BID)) / 2.0;

         if(currentPrice > ma_fast[0])
            return SIGNAL_BUY;
         else
            return SIGNAL_SELL;
      }

      // Final fallback: Use time-based alternation
      datetime currentTime = TimeCurrent();
      if((currentTime % 2) == 0)
         return SIGNAL_BUY;
      else
         return SIGNAL_SELL;
   }
}

//+------------------------------------------------------------------+
//| Get trend direction for specified timeframe                     |
//+------------------------------------------------------------------+
int GetTrendDirection(ENUM_TIMEFRAMES timeframe)
{
   double ma_fast[], ma_slow[], adx[], plus_di[], minus_di[], rsi[];

   // Select appropriate handles based on timeframe
   int ma_fast_handle = (timeframe == PrimaryTimeframe) ? g_handleMA_Fast_Primary : g_handleMA_Fast_Entry;
   int ma_slow_handle = (timeframe == PrimaryTimeframe) ? g_handleMA_Slow_Primary : g_handleMA_Slow_Entry;
   int adx_handle = (timeframe == PrimaryTimeframe) ? g_handleADX_Primary : g_handleADX_Entry;
   int rsi_handle = (timeframe == PrimaryTimeframe) ? g_handleRSI_Primary : g_handleRSI_Entry;

   // Get indicator values
   ArraySetAsSeries(ma_fast, true);
   ArraySetAsSeries(ma_slow, true);
   ArraySetAsSeries(adx, true);
   ArraySetAsSeries(plus_di, true);
   ArraySetAsSeries(minus_di, true);
   ArraySetAsSeries(rsi, true);

   if(CopyBuffer(ma_fast_handle, 0, 0, 3, ma_fast) < 3 ||
      CopyBuffer(ma_slow_handle, 0, 0, 3, ma_slow) < 3 ||
      CopyBuffer(adx_handle, 0, 0, 3, adx) < 3 ||
      CopyBuffer(adx_handle, 1, 0, 3, plus_di) < 3 ||
      CopyBuffer(adx_handle, 2, 0, 3, minus_di) < 3 ||
      CopyBuffer(rsi_handle, 0, 0, 3, rsi) < 3)
   {
      return 0; // Error getting data
   }

   // Trend analysis
   bool ma_uptrend = ma_fast[0] > ma_slow[0] && ma_fast[1] > ma_slow[1];
   bool ma_downtrend = ma_fast[0] < ma_slow[0] && ma_fast[1] < ma_slow[1];
   bool adx_strong = adx[0] > ADX_Threshold;
   bool adx_uptrend = plus_di[0] > minus_di[0];
   bool rsi_bullish = rsi[0] > 50 && rsi[0] < RSI_Overbought;
   bool rsi_bearish = rsi[0] < 50 && rsi[0] > RSI_Oversold;

   // Count bullish signals
   int bullish_count = 0;
   if(ma_uptrend) bullish_count++;
   if(adx_strong && adx_uptrend) bullish_count++;
   if(rsi_bullish) bullish_count++;

   // Count bearish signals
   int bearish_count = 0;
   if(ma_downtrend) bearish_count++;
   if(adx_strong && !adx_uptrend) bearish_count++;
   if(rsi_bearish) bearish_count++;

   // Return trend direction (need at least 2 confirmations)
   if(bullish_count >= 2) return 1;   // Uptrend
   if(bearish_count >= 2) return -1;  // Downtrend
   return 0; // No clear trend
}

//=================================================================
//                    UTILITY FUNCTIONS
//=================================================================

//+------------------------------------------------------------------+
//| Parse allowed symbols from input string                         |
//+------------------------------------------------------------------+
bool ParseAllowedSymbols()
{
   string symbols = AllowedSymbols;
   string separator = ",";

   // Count symbols
   int count = 1;
   for(int i = 0; i < StringLen(symbols); i++)
   {
      if(StringSubstr(symbols, i, 1) == separator)
         count++;
   }

   ArrayResize(g_AllowedSymbolsArray, count);
   g_AllowedSymbolsCount = count;

   // Parse symbols
   int start = 0;
   int index = 0;

   for(int i = 0; i <= StringLen(symbols); i++)
   {
      if(i == StringLen(symbols) || StringSubstr(symbols, i, 1) == separator)
      {
         string symbol = StringSubstr(symbols, start, i - start);
         StringTrimLeft(symbol);
         StringTrimRight(symbol);
         StringToUpper(symbol);

         g_AllowedSymbolsArray[index] = symbol;
         index++;
         start = i + 1;
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Check if symbol is allowed for trading                          |
//+------------------------------------------------------------------+
bool IsSymbolAllowed(string symbol)
{
   StringToUpper(symbol);

   for(int i = 0; i < g_AllowedSymbolsCount; i++)
   {
      if(g_AllowedSymbolsArray[i] == symbol)
         return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check if can open new position                                  |
//+------------------------------------------------------------------+
bool CanOpenNewPosition()
{
   // Check maximum positions
   if(g_PositionCount >= 1) // Only one position at a time for now
      return false;

   // Check time since last trade
   if(TimeCurrent() - g_LastTradeTime < 60) // Wait 1 minute between trades
      return false;

   return true;
}

//+------------------------------------------------------------------+
//| Check daily loss protection                                      |
//+------------------------------------------------------------------+
bool CheckDailyLossProtection()
{
   // Calculate daily loss percentage
   double dailyLossPercent = (g_DailyProfit / g_AccountStartBalance) * 100.0;

   // Check if daily loss exceeds the configured maximum
   if(dailyLossPercent <= -MaxDailyLossPercent)
   {
      g_TradingEnabled = false;
      Print("CRITICAL: Maximum daily loss reached: ", DoubleToString(dailyLossPercent, 2), "%");
      Print("Maximum allowed: ", DoubleToString(MaxDailyLossPercent, 2), "%");
      Print("Trading disabled for today!");
      return false;
   }

   return true;
}

//=================================================================
//                    ADDITIONAL HELPER FUNCTIONS
//=================================================================

//+------------------------------------------------------------------+
//| Get entry signal for specified timeframe                        |
//+------------------------------------------------------------------+
int GetEntrySignal(ENUM_TIMEFRAMES timeframe)
{
   double ma_fast[], ma_slow[], rsi[];

   int ma_fast_handle = (timeframe == PrimaryTimeframe) ? g_handleMA_Fast_Primary : g_handleMA_Fast_Entry;
   int ma_slow_handle = (timeframe == PrimaryTimeframe) ? g_handleMA_Slow_Primary : g_handleMA_Slow_Entry;
   int rsi_handle = (timeframe == PrimaryTimeframe) ? g_handleRSI_Primary : g_handleRSI_Entry;

   ArraySetAsSeries(ma_fast, true);
   ArraySetAsSeries(ma_slow, true);
   ArraySetAsSeries(rsi, true);

   if(CopyBuffer(ma_fast_handle, 0, 0, 2, ma_fast) < 2 ||
      CopyBuffer(ma_slow_handle, 0, 0, 2, ma_slow) < 2 ||
      CopyBuffer(rsi_handle, 0, 0, 2, rsi) < 2)
   {
      return 0;
   }

   // Check for MA crossover
   bool ma_cross_up = (ma_fast[0] > ma_slow[0]) && (ma_fast[1] <= ma_slow[1]);
   bool ma_cross_down = (ma_fast[0] < ma_slow[0]) && (ma_fast[1] >= ma_slow[1]);

   // RSI confirmation
   bool rsi_bullish = rsi[0] > 50 && rsi[0] < RSI_Overbought;
   bool rsi_bearish = rsi[0] < 50 && rsi[0] > RSI_Oversold;

   if(ma_cross_up && rsi_bullish) return 1;   // Buy signal
   if(ma_cross_down && rsi_bearish) return -1; // Sell signal

   return 0; // No signal
}

//+------------------------------------------------------------------+
//| Get price action signal                                          |
//+------------------------------------------------------------------+
int GetPriceActionSignal()
{
   // Get recent price data
   double high[], low[], close[];
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);

   if(CopyHigh(Symbol(), EntryTimeframe, 0, 5, high) < 5 ||
      CopyLow(Symbol(), EntryTimeframe, 0, 5, low) < 5 ||
      CopyClose(Symbol(), EntryTimeframe, 0, 5, close) < 5)
   {
      return 0;
   }

   // Check for bullish patterns
   bool bullishEngulfing = (close[1] < close[2]) && (close[0] > close[1]) &&
                          (close[0] > high[1]) && (low[0] < low[1]);
   bool hammer = (close[0] > (high[0] + low[0]) / 2) &&
                 ((high[0] - close[0]) < (close[0] - low[0]) * 0.3);
   bool higherHighs = (high[0] > high[1]) && (high[1] > high[2]);

   // Check for bearish patterns
   bool bearishEngulfing = (close[1] > close[2]) && (close[0] < close[1]) &&
                          (close[0] < low[1]) && (high[0] > high[1]);
   bool shootingStar = (close[0] < (high[0] + low[0]) / 2) &&
                       ((close[0] - low[0]) < (high[0] - close[0]) * 0.3);
   bool lowerLows = (low[0] < low[1]) && (low[1] < low[2]);

   int bullishCount = 0;
   int bearishCount = 0;

   if(bullishEngulfing) bullishCount++;
   if(hammer) bullishCount++;
   if(higherHighs) bullishCount++;

   if(bearishEngulfing) bearishCount++;
   if(shootingStar) bearishCount++;
   if(lowerLows) bearishCount++;

   if(bullishCount > bearishCount) return 1;
   if(bearishCount > bullishCount) return -1;
   return 0;
}

//+------------------------------------------------------------------+
//| Get momentum signal                                              |
//+------------------------------------------------------------------+
int GetMomentumSignal()
{
   double rsi[], adx[], plus_di[], minus_di[];
   ArraySetAsSeries(rsi, true);
   ArraySetAsSeries(adx, true);
   ArraySetAsSeries(plus_di, true);
   ArraySetAsSeries(minus_di, true);

   if(CopyBuffer(g_handleRSI_Entry, 0, 0, 3, rsi) < 3 ||
      CopyBuffer(g_handleADX_Entry, 0, 0, 3, adx) < 3 ||
      CopyBuffer(g_handleADX_Entry, 1, 0, 3, plus_di) < 3 ||
      CopyBuffer(g_handleADX_Entry, 2, 0, 3, minus_di) < 3)
   {
      return 0;
   }

   // RSI momentum
   bool rsiMomentumUp = (rsi[0] > rsi[1]) && (rsi[1] > rsi[2]) && (rsi[0] > 30);
   bool rsiMomentumDown = (rsi[0] < rsi[1]) && (rsi[1] < rsi[2]) && (rsi[0] < 70);

   // ADX momentum
   bool adxStrengthening = (adx[0] > adx[1]) && (adx[0] > 20);
   bool bullishDI = plus_di[0] > minus_di[0];

   int momentumScore = 0;

   if(rsiMomentumUp) momentumScore++;
   if(rsiMomentumDown) momentumScore--;

   if(adxStrengthening && bullishDI) momentumScore++;
   if(adxStrengthening && !bullishDI) momentumScore--;

   if(momentumScore > 0) return 1;
   if(momentumScore < 0) return -1;
   return 0;
}

//+------------------------------------------------------------------+
//| Get mean reversion signal                                        |
//+------------------------------------------------------------------+
int GetMeanReversionSignal()
{
   double rsi[], bb_upper[], bb_middle[], bb_lower[];
   ArraySetAsSeries(rsi, true);
   ArraySetAsSeries(bb_upper, true);
   ArraySetAsSeries(bb_middle, true);
   ArraySetAsSeries(bb_lower, true);

   // Get Bollinger Bands
   int bbHandle = iBands(Symbol(), EntryTimeframe, 20, 0, 2, PRICE_CLOSE);
   if(bbHandle == INVALID_HANDLE) return 0;

   if(CopyBuffer(g_handleRSI_Entry, 0, 0, 2, rsi) < 2 ||
      CopyBuffer(bbHandle, 0, 0, 2, bb_middle) < 2 ||
      CopyBuffer(bbHandle, 1, 0, 2, bb_upper) < 2 ||
      CopyBuffer(bbHandle, 2, 0, 2, bb_lower) < 2)
   {
      IndicatorRelease(bbHandle);
      return 0;
   }

   double currentPrice = (SymbolInfoDouble(Symbol(), SYMBOL_ASK) + SymbolInfoDouble(Symbol(), SYMBOL_BID)) / 2.0;

   // Oversold conditions (buy signal)
   bool rsiOversold = rsi[0] < 30 && rsi[0] > rsi[1]; // RSI below 30 and rising
   bool priceBelowBB = currentPrice < bb_lower[0];    // Price below lower BB

   // Overbought conditions (sell signal)
   bool rsiOverbought = rsi[0] > 70 && rsi[0] < rsi[1]; // RSI above 70 and falling
   bool priceAboveBB = currentPrice > bb_upper[0];      // Price above upper BB

   IndicatorRelease(bbHandle);

   if(rsiOversold || priceBelowBB) return 1;  // Buy signal
   if(rsiOverbought || priceAboveBB) return -1; // Sell signal
   return 0;
}

//+------------------------------------------------------------------+
//| Get volume-based signal                                          |
//+------------------------------------------------------------------+
int GetVolumeSignal()
{
   long volume[];
   double close[];
   ArraySetAsSeries(volume, true);
   ArraySetAsSeries(close, true);

   if(CopyTickVolume(Symbol(), EntryTimeframe, 0, 5, volume) < 5 ||
      CopyClose(Symbol(), EntryTimeframe, 0, 5, close) < 5)
   {
      return 0;
   }

   // Calculate average volume
   long avgVolume = (volume[1] + volume[2] + volume[3] + volume[4]) / 4;

   // High volume with price movement
   bool highVolumeUp = (volume[0] > avgVolume * 1.5) && (close[0] > close[1]);
   bool highVolumeDown = (volume[0] > avgVolume * 1.5) && (close[0] < close[1]);

   // Volume trend
   bool volumeIncreasing = (volume[0] > volume[1]) && (volume[1] > volume[2]);
   bool priceUp = close[0] > close[2];

   if(highVolumeUp || (volumeIncreasing && priceUp)) return 1;
   if(highVolumeDown || (volumeIncreasing && !priceUp)) return -1;
   return 0;
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk management                |
//+------------------------------------------------------------------+
double CalculatePositionSize(double stopLossDistance)
{
   double lotSize = InitialLotSize;

   if(RiskType == RISK_EQUITY_BASED)
   {
      double accountEquity = AccountInfoDouble(ACCOUNT_EQUITY);
      double riskAmount = accountEquity * (RiskPerTradePercent / 100.0);
      double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
      double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

      if(tickValue > 0 && tickSize > 0 && stopLossDistance > 0)
      {
         double valuePerTick = tickValue / tickSize;
         lotSize = riskAmount / (stopLossDistance * valuePerTick);
      }
   }

   // Apply Anti-Martingale or Fibonacci progression
   if(UseAntiMartingale && g_ConsecutiveWins > 0)
   {
      lotSize *= (1.0 + (g_ConsecutiveWins * 0.1)); // Increase by 10% per win
   }
   else if(UseFibonacciProgression && g_ConsecutiveLosses > 0)
   {
      int fibIndex = MathMin(g_ConsecutiveLosses, ArraySize(g_FibonacciSequence) - 1);
      lotSize *= g_FibonacciSequence[fibIndex];
   }

   // Normalize lot size
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

   return lotSize;
}

//+------------------------------------------------------------------+
//| Calculate stop loss based on ATR                                |
//+------------------------------------------------------------------+
double CalculateStopLoss(ENUM_POSITION_TYPE positionType, double entryPrice)
{
   double atr[];
   ArraySetAsSeries(atr, true);

   if(CopyBuffer(g_handleATR, 0, 0, 1, atr) < 1)
      return 0;

   double atrValue = atr[0];
   double stopLoss = 0;

   if(positionType == POSITION_TYPE_BUY)
   {
      stopLoss = entryPrice - (atrValue * 2.0); // 2 ATR below entry
   }
   else if(positionType == POSITION_TYPE_SELL)
   {
      stopLoss = entryPrice + (atrValue * 2.0); // 2 ATR above entry
   }

   return NormalizeDouble(stopLoss, Digits());
}

//+------------------------------------------------------------------+
//| Calculate take profit                                            |
//+------------------------------------------------------------------+
double CalculateTakeProfit(ENUM_POSITION_TYPE positionType, double entryPrice, double stopLoss)
{
   double stopDistance = MathAbs(entryPrice - stopLoss);
   double takeProfit = 0;

   if(positionType == POSITION_TYPE_BUY)
   {
      takeProfit = entryPrice + (stopDistance * 3.0); // 1:3 risk-reward
   }
   else if(positionType == POSITION_TYPE_SELL)
   {
      takeProfit = entryPrice - (stopDistance * 3.0); // 1:3 risk-reward
   }

   return NormalizeDouble(takeProfit, Digits());
}

//+------------------------------------------------------------------+
//| Open buy position                                                |
//+------------------------------------------------------------------+
void OpenBuyPosition()
{
   double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
   double stopLoss = CalculateStopLoss(POSITION_TYPE_BUY, ask);
   double takeProfit = CalculateTakeProfit(POSITION_TYPE_BUY, ask, stopLoss);
   double stopDistance = ask - stopLoss;
   double lotSize = CalculatePositionSize(stopDistance);

   if(Trade.Buy(lotSize, Symbol(), ask, stopLoss, takeProfit, TradeComment))
   {
      ulong ticket = Trade.ResultOrder();
      AddPositionToArray(ticket, ask, lotSize, stopLoss, takeProfit, POSITION_TYPE_BUY);

      g_LastTradeTime = TimeCurrent();

      if(EnableDetailedLogging)
      {
         Print("BUY position opened: Ticket=", ticket, " Lot=", lotSize,
               " Entry=", ask, " SL=", stopLoss, " TP=", takeProfit);
      }
   }
   else
   {
      Print("ERROR: Failed to open BUY position. Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Open sell position                                               |
//+------------------------------------------------------------------+
void OpenSellPosition()
{
   double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double stopLoss = CalculateStopLoss(POSITION_TYPE_SELL, bid);
   double takeProfit = CalculateTakeProfit(POSITION_TYPE_SELL, bid, stopLoss);
   double stopDistance = stopLoss - bid;
   double lotSize = CalculatePositionSize(stopDistance);

   if(Trade.Sell(lotSize, Symbol(), bid, stopLoss, takeProfit, TradeComment))
   {
      ulong ticket = Trade.ResultOrder();
      AddPositionToArray(ticket, bid, lotSize, stopLoss, takeProfit, POSITION_TYPE_SELL);

      g_LastTradeTime = TimeCurrent();

      if(EnableDetailedLogging)
      {
         Print("SELL position opened: Ticket=", ticket, " Lot=", lotSize,
               " Entry=", bid, " SL=", stopLoss, " TP=", takeProfit);
      }
   }
   else
   {
      Print("ERROR: Failed to open SELL position. Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Add position to tracking array                                  |
//+------------------------------------------------------------------+
void AddPositionToArray(ulong ticket, double openPrice, double lotSize,
                       double stopLoss, double takeProfit, ENUM_POSITION_TYPE type)
{
   ArrayResize(g_CurrentPositions, g_PositionCount + 1);

   g_CurrentPositions[g_PositionCount].ticket = ticket;
   g_CurrentPositions[g_PositionCount].openPrice = openPrice;
   g_CurrentPositions[g_PositionCount].lotSize = lotSize;
   g_CurrentPositions[g_PositionCount].initialSL = stopLoss;
   g_CurrentPositions[g_PositionCount].initialTP = takeProfit;
   g_CurrentPositions[g_PositionCount].partialClosed = false;
   g_CurrentPositions[g_PositionCount].openTime = TimeCurrent();
   g_CurrentPositions[g_PositionCount].type = type;

   // Initialize duration control fields
   g_CurrentPositions[g_PositionCount].maxDurationMinutes = CalculateMaxDuration(type);
   g_CurrentPositions[g_PositionCount].isScalpingTrade = DetermineIfScalpingTrade();
   g_CurrentPositions[g_PositionCount].entryVolatility = GetCurrentATR();
   g_CurrentPositions[g_PositionCount].durationWarningIssued = false;
   g_CurrentPositions[g_PositionCount].lastTrailingUpdate = 0;
   g_CurrentPositions[g_PositionCount].lastTrailingPrice = 0.0;

   g_PositionCount++;
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManageExistingPositions()
{
   for(int i = g_PositionCount - 1; i >= 0; i--)
   {
      if(PositionSelectByTicket(g_CurrentPositions[i].ticket))
      {
         double currentProfit = PositionGetDouble(POSITION_PROFIT);
         double openPrice = g_CurrentPositions[i].openPrice;
         double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);

         // Handle partial profit taking
         if(EnablePartialProfitTaking && !g_CurrentPositions[i].partialClosed)
         {
            HandlePartialProfitTaking(i, currentProfit, openPrice, currentPrice);
         }

         // Handle trailing stop
         if(EnableTrailingStop)
         {
            if(EnableSmartTrailing)
            {
               HandleSmartTrailingStop(i, currentPrice);
            }
            else
            {
               HandleTrailingStop(i, currentPrice);
            }
         }

         // Handle breakeven
         HandleBreakeven(i, currentPrice);

         // Handle trade duration monitoring
         if(EnableTradeDurationControl)
         {
            CheckTradeDuration(i);
         }
      }
      else
      {
         // Position closed, update statistics
         UpdateTradeStatistics(i);
         RemovePositionFromArray(i);
      }
   }
}

//+------------------------------------------------------------------+
//| Handle partial profit taking                                     |
//+------------------------------------------------------------------+
void HandlePartialProfitTaking(int posIndex, double currentProfit,
                              double openPrice, double currentPrice)
{
   double stopDistance = MathAbs(openPrice - g_CurrentPositions[posIndex].initialSL);
   double profitDistance = MathAbs(currentPrice - openPrice);

   // Close 50% when profit equals stop distance (1:1 RR)
   if(profitDistance >= stopDistance)
   {
      double partialLot = g_CurrentPositions[posIndex].lotSize * (PartialClosePercent / 100.0);
      partialLot = NormalizeDouble(partialLot, 2);

      if(Trade.PositionClosePartial(g_CurrentPositions[posIndex].ticket, partialLot))
      {
         g_CurrentPositions[posIndex].partialClosed = true;
         g_CurrentPositions[posIndex].lotSize -= partialLot;

         if(EnableDetailedLogging)
         {
            Print("Partial close executed: Ticket=", g_CurrentPositions[posIndex].ticket,
                  " Closed=", partialLot, " Remaining=", g_CurrentPositions[posIndex].lotSize);
         }
      }
   }
}



//+------------------------------------------------------------------+
//| Handle trailing stop                                             |
//+------------------------------------------------------------------+
void HandleTrailingStop(int posIndex, double currentPrice)
{
   double atr[];
   ArraySetAsSeries(atr, true);

   if(CopyBuffer(g_handleATR, 0, 0, 1, atr) < 1)
      return;

   double atrValue = atr[0];
   double newStopLoss = 0;

   if(g_CurrentPositions[posIndex].type == POSITION_TYPE_BUY)
   {
      newStopLoss = currentPrice - (atrValue * 1.5); // 1.5 ATR trailing

      if(newStopLoss > g_CurrentPositions[posIndex].initialSL)
      {
         newStopLoss = NormalizeDouble(newStopLoss, Digits());

         if(Trade.PositionModify(g_CurrentPositions[posIndex].ticket, newStopLoss,
                                g_CurrentPositions[posIndex].initialTP))
         {
            g_CurrentPositions[posIndex].initialSL = newStopLoss;

            if(EnableDetailedLogging)
            {
               Print("Trailing stop updated for BUY position: ",
                     g_CurrentPositions[posIndex].ticket, " New SL: ", newStopLoss);
            }
         }
      }
   }
   else if(g_CurrentPositions[posIndex].type == POSITION_TYPE_SELL)
   {
      newStopLoss = currentPrice + (atrValue * 1.5); // 1.5 ATR trailing

      if(newStopLoss < g_CurrentPositions[posIndex].initialSL)
      {
         newStopLoss = NormalizeDouble(newStopLoss, Digits());

         if(Trade.PositionModify(g_CurrentPositions[posIndex].ticket, newStopLoss,
                                g_CurrentPositions[posIndex].initialTP))
         {
            g_CurrentPositions[posIndex].initialSL = newStopLoss;

            if(EnableDetailedLogging)
            {
               Print("Trailing stop updated for SELL position: ",
                     g_CurrentPositions[posIndex].ticket, " New SL: ", newStopLoss);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Handle smart trailing stop with throttling                      |
//+------------------------------------------------------------------+
void HandleSmartTrailingStop(int posIndex, double currentPrice)
{
   datetime currentTime = TimeCurrent();

   // Check throttling - don't update too frequently
   if(g_CurrentPositions[posIndex].lastTrailingUpdate > 0)
   {
      int secondsSinceLastUpdate = (int)(currentTime - g_CurrentPositions[posIndex].lastTrailingUpdate);
      if(secondsSinceLastUpdate < TrailingStopThrottleSeconds)
      {
         return; // Too soon to update
      }
   }

   double atr[];
   ArraySetAsSeries(atr, true);

   if(CopyBuffer(g_handleATR, 0, 0, 1, atr) < 1)
      return;

   double atrValue = atr[0];
   double newStopLoss = 0;
   bool shouldUpdate = false;

   if(g_CurrentPositions[posIndex].type == POSITION_TYPE_BUY)
   {
      newStopLoss = currentPrice - (atrValue * 1.5); // 1.5 ATR trailing

      // Check if new SL is better than current and meets minimum step
      if(newStopLoss > g_CurrentPositions[posIndex].initialSL)
      {
         double stepPoints = (newStopLoss - g_CurrentPositions[posIndex].initialSL) / Point();
         if(stepPoints >= MinTrailingStepPoints)
         {
            shouldUpdate = true;
         }
      }
   }
   else if(g_CurrentPositions[posIndex].type == POSITION_TYPE_SELL)
   {
      newStopLoss = currentPrice + (atrValue * 1.5); // 1.5 ATR trailing

      // Check if new SL is better than current and meets minimum step
      if(newStopLoss < g_CurrentPositions[posIndex].initialSL)
      {
         double stepPoints = (g_CurrentPositions[posIndex].initialSL - newStopLoss) / Point();
         if(stepPoints >= MinTrailingStepPoints)
         {
            shouldUpdate = true;
         }
      }
   }

   if(shouldUpdate)
   {
      newStopLoss = NormalizeDouble(newStopLoss, Digits());

      if(Trade.PositionModify(g_CurrentPositions[posIndex].ticket, newStopLoss,
                             g_CurrentPositions[posIndex].initialTP))
      {
         g_CurrentPositions[posIndex].initialSL = newStopLoss;
         g_CurrentPositions[posIndex].lastTrailingUpdate = currentTime;
         g_CurrentPositions[posIndex].lastTrailingPrice = currentPrice;

         if(EnableDetailedLogging)
         {
            string posType = (g_CurrentPositions[posIndex].type == POSITION_TYPE_BUY) ? "BUY" : "SELL";
            Print("Smart trailing stop updated for ", posType, " position: ",
                  g_CurrentPositions[posIndex].ticket, " New SL: ", newStopLoss,
                  " (", TrailingStopThrottleSeconds, "s throttle)");
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Handle breakeven                                                 |
//+------------------------------------------------------------------+
void HandleBreakeven(int posIndex, double currentPrice)
{
   double openPrice = g_CurrentPositions[posIndex].openPrice;
   double stopDistance = MathAbs(openPrice - g_CurrentPositions[posIndex].initialSL);
   double profitDistance = 0;

   if(g_CurrentPositions[posIndex].type == POSITION_TYPE_BUY)
   {
      profitDistance = currentPrice - openPrice;
   }
   else if(g_CurrentPositions[posIndex].type == POSITION_TYPE_SELL)
   {
      profitDistance = openPrice - currentPrice;
   }

   // Move to breakeven when profit is 1.5x stop distance
   if(profitDistance >= (stopDistance * 1.5))
   {
      double newStopLoss = NormalizeDouble(openPrice, Digits());

      // Only move if current SL is worse than breakeven
      bool shouldMove = false;

      if(g_CurrentPositions[posIndex].type == POSITION_TYPE_BUY &&
         newStopLoss > g_CurrentPositions[posIndex].initialSL)
      {
         shouldMove = true;
      }
      else if(g_CurrentPositions[posIndex].type == POSITION_TYPE_SELL &&
              newStopLoss < g_CurrentPositions[posIndex].initialSL)
      {
         shouldMove = true;
      }

      if(shouldMove)
      {
         if(Trade.PositionModify(g_CurrentPositions[posIndex].ticket, newStopLoss,
                                g_CurrentPositions[posIndex].initialTP))
         {
            g_CurrentPositions[posIndex].initialSL = newStopLoss;

            if(EnableDetailedLogging)
            {
               Print("Position moved to breakeven: ", g_CurrentPositions[posIndex].ticket);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Update trade statistics                                          |
//+------------------------------------------------------------------+
void UpdateTradeStatistics(int posIndex)
{
   // Get position information before it's closed
   ulong ticket = g_CurrentPositions[posIndex].ticket;
   double openPrice = g_CurrentPositions[posIndex].openPrice;
   datetime openTime = g_CurrentPositions[posIndex].openTime;
   ENUM_POSITION_TYPE posType = g_CurrentPositions[posIndex].type;

   // Try to get the final profit from position history
   double profit = 0;
   bool found = false;

   // First check if position is still open
   if(PositionSelectByTicket(ticket))
   {
      profit = PositionGetDouble(POSITION_PROFIT);
      found = true;
   }
   else
   {
      // Position is closed, check history
      HistorySelect(openTime, TimeCurrent());

      // Look for the deal that closed this position
      for(int i = 0; i < HistoryDealsTotal(); i++)
      {
         ulong dealTicket = HistoryDealGetTicket(i);
         if(dealTicket > 0)
         {
            if(HistoryDealGetInteger(dealTicket, DEAL_POSITION_ID) == ticket)
            {
               ENUM_DEAL_ENTRY dealEntry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(dealTicket, DEAL_ENTRY);
               if(dealEntry == DEAL_ENTRY_OUT || dealEntry == DEAL_ENTRY_OUT_BY)
               {
                  profit = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);
                  found = true;
                  break;
               }
            }
         }
      }
   }

   // If we couldn't find the exact profit, estimate it
   if(!found)
   {
      double currentPrice = (posType == POSITION_TYPE_BUY) ?
                           SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                           SymbolInfoDouble(Symbol(), SYMBOL_ASK);

      double priceDiff = (posType == POSITION_TYPE_BUY) ?
                        (currentPrice - openPrice) :
                        (openPrice - currentPrice);

      double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
      double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

      if(tickValue > 0 && tickSize > 0)
      {
         profit = (priceDiff / tickSize) * tickValue * g_CurrentPositions[posIndex].lotSize;
      }
   }

   // Update statistics
   g_TodayStats.totalTrades++;
   g_TodayStats.profit += profit;
   g_DailyProfit += profit;

   if(profit > 0)
   {
      g_TodayStats.winningTrades++;
      g_ConsecutiveWins++;
      g_ConsecutiveLosses = 0;

      if(EnableDetailedLogging)
      {
         Print("WINNING TRADE: Ticket=", ticket, " Profit=$", DoubleToString(profit, 2));
         Print("Consecutive wins: ", g_ConsecutiveWins);
      }
   }
   else
   {
      g_TodayStats.losingTrades++;
      g_ConsecutiveLosses++;
      g_ConsecutiveWins = 0;

      if(EnableDetailedLogging)
      {
         Print("LOSING TRADE: Ticket=", ticket, " Loss=$", DoubleToString(profit, 2));
         Print("Consecutive losses: ", g_ConsecutiveLosses);
      }

      // Check if need to pause trading due to consecutive losses
      if(g_ConsecutiveLosses >= MaxConsecutiveLosses)
      {
         g_TradingPauseUntil = TimeCurrent() + (3600 * 2); // Pause for 2 hours
         Print("TRADING PAUSED: ", g_ConsecutiveLosses, " consecutive losses reached");
         Print("Trading will resume at: ", TimeToString(g_TradingPauseUntil));
      }
   }

   // Update max drawdown if necessary
   double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
   double currentDrawdown = ((g_AccountStartBalance - currentEquity) / g_AccountStartBalance) * 100.0;

   if(currentDrawdown > g_TodayStats.maxDrawdown)
   {
      g_TodayStats.maxDrawdown = currentDrawdown;
   }

   if(currentDrawdown > g_MaxDrawdownReached)
   {
      g_MaxDrawdownReached = currentDrawdown;
   }

   // Log trade summary
   if(EnableDetailedLogging)
   {
      // Calculate trade duration
      int tradeDurationMinutes = (int)((TimeCurrent() - openTime) / 60);

      Print("=== TRADE COMPLETED ===");
      Print("Ticket: ", ticket);
      Print("Type: ", (posType == POSITION_TYPE_BUY) ? "BUY" : "SELL");
      Print("Lot Size: ", DoubleToString(g_CurrentPositions[posIndex].lotSize, 2));
      Print("Open Price: ", DoubleToString(openPrice, Digits()));
      Print("Duration: ", tradeDurationMinutes, " minutes");
      Print("Max Duration: ", g_CurrentPositions[posIndex].maxDurationMinutes, " minutes");
      Print("Trade Type: ", g_CurrentPositions[posIndex].isScalpingTrade ? "SCALPING" : "TREND");
      Print("Entry Volatility (ATR): ", DoubleToString(g_CurrentPositions[posIndex].entryVolatility, Digits()));
      Print("Profit: $", DoubleToString(profit, 2));
      Print("Daily Profit: $", DoubleToString(g_DailyProfit, 2));
      Print("Daily Target: $", DoubleToString(g_DailyTarget, 2));
      Print("Win Rate: ", g_TodayStats.totalTrades > 0 ?
            DoubleToString((double)g_TodayStats.winningTrades / g_TodayStats.totalTrades * 100, 1) : "0", "%");
      Print("Current Drawdown: ", DoubleToString(currentDrawdown, 2), "%");
      Print("=====================");
   }
}

//+------------------------------------------------------------------+
//| Remove position from tracking array                             |
//+------------------------------------------------------------------+
void RemovePositionFromArray(int index)
{
   if(index < 0 || index >= g_PositionCount)
      return;

   // Shift all elements after the removed index
   for(int i = index; i < g_PositionCount - 1; i++)
   {
      g_CurrentPositions[i] = g_CurrentPositions[i + 1];
   }

   // Decrease count and resize array
   g_PositionCount--;
   ArrayResize(g_CurrentPositions, g_PositionCount);

   if(EnableDetailedLogging)
   {
      Print("Position removed from tracking array. Active positions: ", g_PositionCount);
   }
}
//+------------------------------------------------------------------+
//| Check if new day started                                         |
//+------------------------------------------------------------------+
void CheckNewDay()
{
   static int lastDay = -1;

   MqlDateTime timeStruct;
   TimeToStruct(TimeCurrent(), timeStruct);

   if(lastDay != timeStruct.day)
   {
      if(lastDay != -1) // Not first run
      {
         // New day started, reset daily statistics
         PrintDailyReport();
         ResetDailyStats();
      }
      lastDay = timeStruct.day;
   }
}

//+------------------------------------------------------------------+
//| Update daily statistics                                          |
//+------------------------------------------------------------------+
void UpdateDailyStats()
{
   double currentProfit = 0;

   // Calculate total profit from all positions
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         {
            currentProfit += PositionGetDouble(POSITION_PROFIT);
         }
      }
   }

   g_DailyProfit = currentProfit;

   // Check if daily target reached
   if(!g_DailyTargetReached && g_DailyProfit >= g_DailyTarget)
   {
      g_DailyTargetReached = true;
      Print("DAILY TARGET REACHED! Profit: $", DoubleToString(g_DailyProfit, 2));
      Print("Trading suspended for today");
   }
}

//+------------------------------------------------------------------+
//| Reset daily statistics                                           |
//+------------------------------------------------------------------+
void ResetDailyStats()
{
   g_TodayStats.date = TimeCurrent();
   g_TodayStats.profit = 0;
   g_TodayStats.totalTrades = 0;
   g_TodayStats.winningTrades = 0;
   g_TodayStats.losingTrades = 0;
   g_TodayStats.maxDrawdown = 0;

   g_DailyProfit = 0;
   g_DailyTargetReached = false;
   g_ConsecutiveLosses = 0;
   g_ConsecutiveWins = 0;
   g_MaxDrawdownReached = 0;

   // Re-enable trading if it was disabled
   if(!g_TradingEnabled && TimeCurrent() > g_TradingPauseUntil)
   {
      g_TradingEnabled = true;
      Print("Trading re-enabled for new day");
   }
}

//+------------------------------------------------------------------+
//| Check if within trading session                                 |
//+------------------------------------------------------------------+
bool IsWithinTradingSession()
{
   if(!EnableSessionFilter)
      return true;

   datetime currentTime = TimeCurrent();
   MqlDateTime timeStruct;
   TimeToStruct(currentTime, timeStruct);

   // Convert time strings to minutes
   string startParts[];
   string endParts[];
   StringSplit(TradingStartTime, ':', startParts);
   StringSplit(TradingEndTime, ':', endParts);

   int startMinutes = (int)StringToInteger(startParts[0]) * 60 + (int)StringToInteger(startParts[1]);
   int endMinutes = (int)StringToInteger(endParts[0]) * 60 + (int)StringToInteger(endParts[1]);
   int currentMinutes = timeStruct.hour * 60 + timeStruct.min;

   if(startMinutes <= endMinutes)
   {
      return (currentMinutes >= startMinutes && currentMinutes <= endMinutes);
   }
   else // Overnight session
   {
      return (currentMinutes >= startMinutes || currentMinutes <= endMinutes);
   }
}

//+------------------------------------------------------------------+
//| Check if it's news time                                          |
//+------------------------------------------------------------------+
bool IsNewsTime()
{
   if(!EnableNewsFilter)
      return false;

   // Simplified news filter - avoid typical high-impact news times
   datetime currentTime = TimeCurrent();
   MqlDateTime timeStruct;
   TimeToStruct(currentTime, timeStruct);

   int currentHour = timeStruct.hour;
   int currentMin = timeStruct.min;

   // Check for 8:30 AM EST (13:30 GMT) ± NewsFilterMinutes
   if(currentHour == 13 && MathAbs(currentMin - 30) <= NewsFilterMinutes)
      return true;

   // Check for 2:00 PM EST (19:00 GMT) ± NewsFilterMinutes
   if(currentHour == 19 && currentMin <= NewsFilterMinutes)
      return true;

   return false;
}

//+------------------------------------------------------------------+
//| Log trading status                                               |
//+------------------------------------------------------------------+
void LogTradingStatus()
{
   if(!EnableDetailedLogging)
      return;

   Print("--- TRADING STATUS ---");
   Print("Time: ", TimeToString(TimeCurrent()));
   Print("Trading Enabled: ", g_TradingEnabled ? "YES" : "NO");
   Print("Daily Target Reached: ", g_DailyTargetReached ? "YES" : "NO");
   Print("Active Positions: ", g_PositionCount);
   Print("Daily Profit: $", DoubleToString(g_DailyProfit, 2));
   Print("Daily Target: $", DoubleToString(g_DailyTarget, 2));

   double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
   double currentDrawdown = ((g_AccountStartBalance - currentEquity) / g_AccountStartBalance) * 100.0;
   Print("Current Drawdown: ", DoubleToString(currentDrawdown, 2), "%");
   Print("Consecutive Wins: ", g_ConsecutiveWins);
   Print("Consecutive Losses: ", g_ConsecutiveLosses);
   Print("--- END STATUS ---");
}

//+------------------------------------------------------------------+
//| Print initialization information                                 |
//+------------------------------------------------------------------+
void PrintInitializationInfo()
{
   Print("=================================================================");
   Print("           ENHANCED TRADING EA v2.0 - INITIALIZATION");
   Print("=================================================================");
   Print("Account Balance: $", DoubleToString(g_AccountStartBalance, 2));
   Print("Daily Profit Target: $", DoubleToString(g_DailyTarget, 2));
   Print("Max Daily Loss: ", DoubleToString(MaxDailyLossPercent, 1), "%");
   Print("Risk Per Trade: ", DoubleToString(RiskPerTradePercent, 1), "%");
   Print("Current Symbol: ", Symbol());
   Print("Magic Number: ", MagicNumber);
   Print("Trade Duration Control: ", EnableTradeDurationControl ? "ENABLED" : "DISABLED");
   if(EnableTradeDurationControl)
   {
      Print("Max Trade Duration: ", MaxTradeMinutes, " minutes");
      Print("Scalping Max Duration: ", ScalpingMaxMinutes, " minutes");
      Print("Trend Max Duration: ", TrendMaxMinutes, " minutes");
      Print("Adaptive Duration: ", UseAdaptiveDuration ? "ENABLED" : "DISABLED");
   }
   Print("Smart Trailing Stop: ", EnableSmartTrailing ? "ENABLED" : "DISABLED");
   if(EnableSmartTrailing)
   {
      Print("Trailing Throttle: ", TrailingStopThrottleSeconds, " seconds");
      Print("Min Trailing Step: ", MinTrailingStepPoints, " points");
   }
   Print("=================================================================");
   Print("EA initialization completed successfully!");
   Print("=================================================================");
}

//+------------------------------------------------------------------+
//| Print final statistics                                           |
//+------------------------------------------------------------------+
void PrintFinalStats()
{
   Print("=================================================================");
   Print("           ENHANCED TRADING EA v2.0 - FINAL STATISTICS");
   Print("=================================================================");
   Print("Total Trades Today: ", g_TodayStats.totalTrades);
   Print("Winning Trades: ", g_TodayStats.winningTrades);
   Print("Losing Trades: ", g_TodayStats.losingTrades);
   Print("Daily Profit: $", DoubleToString(g_TodayStats.profit, 2));
   Print("Max Drawdown Reached: ", DoubleToString(g_MaxDrawdownReached, 2), "%");
   Print("=================================================================");
}

//+------------------------------------------------------------------+
//| Check trade duration and close if too long                      |
//+------------------------------------------------------------------+
void CheckTradeDuration(int posIndex)
{
   datetime currentTime = TimeCurrent();
   datetime openTime = g_CurrentPositions[posIndex].openTime;
   int durationMinutes = (int)((currentTime - openTime) / 60);
   int maxDuration = g_CurrentPositions[posIndex].maxDurationMinutes;

   // Issue warning at 75% of max duration
   int warningThreshold = (int)(maxDuration * 0.75);

   if(!g_CurrentPositions[posIndex].durationWarningIssued && durationMinutes >= warningThreshold)
   {
      g_CurrentPositions[posIndex].durationWarningIssued = true;

      if(EnableDetailedLogging)
      {
         Print("DURATION WARNING: Position ", g_CurrentPositions[posIndex].ticket,
               " has been open for ", durationMinutes, " minutes (", warningThreshold, " threshold)");
         Print("Max duration: ", maxDuration, " minutes. Will force close if not closed soon.");
      }
   }

   // Force close if duration exceeded
   if(durationMinutes >= maxDuration)
   {
      if(ForceCloseAtDuration)
      {
         Print("FORCE CLOSING: Position ", g_CurrentPositions[posIndex].ticket,
               " exceeded max duration of ", maxDuration, " minutes (actual: ", durationMinutes, ")");

         if(Trade.PositionClose(g_CurrentPositions[posIndex].ticket))
         {
            Print("Position successfully closed due to duration limit");
         }
         else
         {
            Print("ERROR: Failed to close position due to duration limit. Error: ", GetLastError());
         }
      }
      else
      {
         Print("DURATION EXCEEDED: Position ", g_CurrentPositions[posIndex].ticket,
               " has exceeded max duration but ForceCloseAtDuration is disabled");
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate maximum duration for a trade                          |
//+------------------------------------------------------------------+
int CalculateMaxDuration(ENUM_POSITION_TYPE posType)
{
   if(!UseAdaptiveDuration)
   {
      return MaxTradeMinutes;
   }

   // Get current market volatility
   double currentATR = GetCurrentATR();
   double avgATR = GetAverageATR();

   // Determine if this is likely a scalping or trend trade
   bool isScalping = DetermineIfScalpingTrade();

   int baseDuration;
   if(isScalping)
   {
      baseDuration = ScalpingMaxMinutes;
   }
   else
   {
      baseDuration = TrendMaxMinutes;
   }

   // Adjust based on volatility
   double volatilityRatio = (avgATR > 0) ? (currentATR / avgATR) : 1.0;

   // High volatility = shorter duration, Low volatility = longer duration
   if(volatilityRatio > 1.5)
   {
      baseDuration = (int)(baseDuration * 0.7); // Reduce by 30%
   }
   else if(volatilityRatio < 0.7)
   {
      baseDuration = (int)(baseDuration * 1.3); // Increase by 30%
   }

   // Ensure within bounds
   baseDuration = MathMax(15, MathMin(MaxTradeMinutes, baseDuration)); // Min 15 minutes

   return baseDuration;
}

//+------------------------------------------------------------------+
//| Determine if current trade should be treated as scalping        |
//+------------------------------------------------------------------+
bool DetermineIfScalpingTrade()
{
   // Check recent price movement and volatility
   double atr = GetCurrentATR();
   double avgATR = GetAverageATR();

   // Get recent RSI to determine market momentum
   double rsi[];
   ArraySetAsSeries(rsi, true);

   if(CopyBuffer(g_handleRSI_Entry, 0, 0, 1, rsi) > 0)
   {
      // Scalping conditions:
      // 1. Low volatility (ATR below average)
      // 2. RSI near extremes (potential reversal)
      // 3. Small price movements expected

      bool lowVolatility = (avgATR > 0) && (atr < avgATR * 0.8);
      bool nearExtremes = (rsi[0] < 25 || rsi[0] > 75);

      return (lowVolatility || nearExtremes);
   }

   // Default to trend trading if can't determine
   return false;
}

//+------------------------------------------------------------------+
//| Get current ATR value                                            |
//+------------------------------------------------------------------+
double GetCurrentATR()
{
   double atr[];
   ArraySetAsSeries(atr, true);

   if(CopyBuffer(g_handleATR, 0, 0, 1, atr) > 0)
   {
      return atr[0];
   }

   return 0.0;
}

//+------------------------------------------------------------------+
//| Get average ATR over last 20 periods                            |
//+------------------------------------------------------------------+
double GetAverageATR()
{
   double atr[];
   ArraySetAsSeries(atr, true);

   if(CopyBuffer(g_handleATR, 0, 0, 20, atr) >= 20)
   {
      double sum = 0;
      for(int i = 0; i < 20; i++)
      {
         sum += atr[i];
      }
      return sum / 20.0;
   }

   return 0.0;
}

//+------------------------------------------------------------------+
//| Print daily report                                               |
//+------------------------------------------------------------------+
void PrintDailyReport()
{
   Print("=================================================================");
   Print("                    DAILY TRADING REPORT");
   Print("=================================================================");
   Print("Date: ", TimeToString(g_TodayStats.date, TIME_DATE));
   Print("Total Trades: ", g_TodayStats.totalTrades);
   Print("Winning Trades: ", g_TodayStats.winningTrades);
   Print("Losing Trades: ", g_TodayStats.losingTrades);
   Print("Daily Profit: $", DoubleToString(g_TodayStats.profit, 2));
   Print("Daily Target: $", DoubleToString(g_DailyTarget, 2));
   Print("Target Achievement: ", g_DailyTargetReached ? "ACHIEVED" : "NOT ACHIEVED");
   Print("=================================================================");
}

//+------------------------------------------------------------------+
//| END OF ENHANCED TRADING EA v2.0                                 |
//+------------------------------------------------------------------+