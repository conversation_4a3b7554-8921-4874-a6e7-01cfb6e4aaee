//+------------------------------------------------------------------+
//| ImprovedTrendFollower.mq5                                        |
//| Copyright 2025, Expert Trading Solutions                         |
//| Improved EA based on best practices and risk management          |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Expert Trading Solutions"
#property link      "https://www.example.com"
#property version   "2.00"
#property strict

#include <Trade\Trade.mqh>
#include <Math\Stat\Math.mqh>

//--- Input Parameters
input group "=== Risk Management ==="
input double RiskPercentPerTrade = 1.0;      // Risk per trade (% of account)
input double MaxDailyLossPercent = 3.0;      // Maximum daily loss (% of account)
input int    MaxConsecutiveLosses = 3;       // Max consecutive losses before pause
input double MaxPositionSizePercent = 5.0;   // Maximum position size (% of account)

input group "=== Trading Parameters ==="
input ENUM_TIMEFRAMES TrendTimeframe = PERIOD_H1;    // Trend analysis timeframe
input ENUM_TIMEFRAMES EntryTimeframe = PERIOD_M15;   // Entry signal timeframe
input int    EMAPeriod = 20;                 // EMA period for trend
input int    RSIPeriod = 14;                 // RSI period
input int    ATRPeriod = 14;                 // ATR period
input double ATRStopMultiplier = 1.5;        // ATR multiplier for stop loss
input double ATRTargetMultiplier = 3.0;      // ATR multiplier for take profit

input group "=== Session Filters ==="
input bool   UseSessionFilter = true;        // Enable session filtering
input string TradingStartTime = "08:00";     // Trading session start (broker time)
input string TradingEndTime = "18:00";       // Trading session end (broker time)

input group "=== Advanced Settings ==="
input bool   UseTrailingStop = true;         // Enable trailing stop
input double TrailingStopATR = 2.0;          // Trailing stop ATR multiplier
input int    MagicNumber = 123456;           // Magic number
input string TradeComment = "ImprovedEA";    // Trade comment

//--- Global Variables
CTrade Trade;
datetime LastTradeDay = 0;
double DailyPnL = 0.0;
int ConsecutiveLosses = 0;
bool TradingPaused = false;

// Indicator handles
int EMAHandle = INVALID_HANDLE;
int RSIHandle = INVALID_HANDLE;
int ATRHandle = INVALID_HANDLE;
int EMAEntryHandle = INVALID_HANDLE;
int RSIEntryHandle = INVALID_HANDLE;
int ATREntryHandle = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Validate inputs
    if(!ValidateInputs())
    {
        Print("Invalid input parameters!");
        return INIT_PARAMETERS_INCORRECT;
    }
    
    // Initialize trade object
    Trade.SetExpertMagicNumber(MagicNumber);
    Trade.SetDeviationInPoints(10);
    Trade.SetTypeFilling(ORDER_FILLING_FOK);
    Trade.SetAsyncMode(false);
    
    // Initialize indicators for trend analysis
    EMAHandle = iMA(Symbol(), TrendTimeframe, EMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
    RSIHandle = iRSI(Symbol(), TrendTimeframe, RSIPeriod, PRICE_CLOSE);
    ATRHandle = iATR(Symbol(), TrendTimeframe, ATRPeriod);
    
    // Initialize indicators for entry signals
    EMAEntryHandle = iMA(Symbol(), EntryTimeframe, EMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
    RSIEntryHandle = iRSI(Symbol(), EntryTimeframe, RSIPeriod, PRICE_CLOSE);
    ATREntryHandle = iATR(Symbol(), EntryTimeframe, ATRPeriod);
    
    if(EMAHandle == INVALID_HANDLE || RSIHandle == INVALID_HANDLE || 
       ATRHandle == INVALID_HANDLE || EMAEntryHandle == INVALID_HANDLE ||
       RSIEntryHandle == INVALID_HANDLE || ATREntryHandle == INVALID_HANDLE)
    {
        Print("Error creating indicator handles");
        return INIT_FAILED;
    }
    
    Print("=== Improved Trend Follower EA Initialized ===");
    Print("Risk per trade: ", RiskPercentPerTrade, "%");
    Print("Max daily loss: ", MaxDailyLossPercent, "%");
    Print("Trend timeframe: ", EnumToString(TrendTimeframe));
    Print("Entry timeframe: ", EnumToString(EntryTimeframe));
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    if(EMAHandle != INVALID_HANDLE) IndicatorRelease(EMAHandle);
    if(RSIHandle != INVALID_HANDLE) IndicatorRelease(RSIHandle);
    if(ATRHandle != INVALID_HANDLE) IndicatorRelease(ATRHandle);
    if(EMAEntryHandle != INVALID_HANDLE) IndicatorRelease(EMAEntryHandle);
    if(RSIEntryHandle != INVALID_HANDLE) IndicatorRelease(RSIEntryHandle);
    if(ATREntryHandle != INVALID_HANDLE) IndicatorRelease(ATREntryHandle);
    
    Print("EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check for new day and reset daily tracking
    CheckNewTradingDay();
    
    // Check daily loss limit
    if(CheckDailyLossLimit())
        return;
    
    // Check session filter
    if(!IsWithinTradingSession())
        return;
    
    // Check if trading is paused due to consecutive losses
    if(TradingPaused)
        return;
    
    // Update trailing stops for existing positions
    if(UseTrailingStop)
        UpdateTrailingStops();
    
    // Check for new trade opportunities
    if(PositionsTotal() == 0 || CountOpenPositions() == 0)
    {
        CheckForTradeSignals();
    }
}

//+------------------------------------------------------------------+
//| Validate input parameters                                        |
//+------------------------------------------------------------------+
bool ValidateInputs()
{
    if(RiskPercentPerTrade <= 0 || RiskPercentPerTrade > 10)
    {
        Print("Risk per trade must be between 0.1% and 10%");
        return false;
    }
    
    if(MaxDailyLossPercent <= 0 || MaxDailyLossPercent > 20)
    {
        Print("Max daily loss must be between 0.1% and 20%");
        return false;
    }
    
    if(EMAPeriod < 5 || EMAPeriod > 200)
    {
        Print("EMA period must be between 5 and 200");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check for new trading day                                        |
//+------------------------------------------------------------------+
void CheckNewTradingDay()
{
    datetime currentDay = (datetime)(TimeCurrent() / 86400) * 86400;
    
    if(currentDay != LastTradeDay && LastTradeDay != 0)
    {
        Print("=== New Trading Day ===");
        Print("Previous day P&L: $", DoubleToString(DailyPnL, 2));
        
        // Reset daily tracking
        DailyPnL = 0.0;
        ConsecutiveLosses = 0;
        TradingPaused = false;
    }
    
    LastTradeDay = currentDay;
}

//+------------------------------------------------------------------+
//| Check daily loss limit                                           |
//+------------------------------------------------------------------+
bool CheckDailyLossLimit()
{
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double maxDailyLoss = accountBalance * MaxDailyLossPercent / 100.0;
    
    if(DailyPnL <= -maxDailyLoss)
    {
        if(!TradingPaused)
        {
            Print("Daily loss limit reached: $", DoubleToString(DailyPnL, 2));
            Print("Trading paused for today");
            TradingPaused = true;
            
            // Close all positions
            CloseAllPositions();
        }
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Check if within trading session                                  |
//+------------------------------------------------------------------+
bool IsWithinTradingSession()
{
    if(!UseSessionFilter)
        return true;
    
    datetime currentTime = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(currentTime, dt);
    
    // Convert string times to minutes
    string startParts[];
    string endParts[];
    StringSplit(TradingStartTime, ':', startParts);
    StringSplit(TradingEndTime, ':', endParts);
    
    int startMinutes = (int)StringToInteger(startParts[0]) * 60 + (int)StringToInteger(startParts[1]);
    int endMinutes = (int)StringToInteger(endParts[0]) * 60 + (int)StringToInteger(endParts[1]);
    int currentMinutes = dt.hour * 60 + dt.min;
    
    return (currentMinutes >= startMinutes && currentMinutes <= endMinutes);
}

//+------------------------------------------------------------------+
//| Check for trade signals                                          |
//+------------------------------------------------------------------+
void CheckForTradeSignals()
{
    // Get trend direction from higher timeframe
    int trendDirection = GetTrendDirection();
    
    if(trendDirection == 0)
        return; // No clear trend
    
    // Get entry signal from lower timeframe
    int entrySignal = GetEntrySignal(trendDirection);
    
    if(entrySignal != 0)
    {
        if(entrySignal == 1)
            OpenBuyOrder();
        else if(entrySignal == -1)
            OpenSellOrder();
    }
}

//+------------------------------------------------------------------+
//| Get trend direction from higher timeframe                        |
//+------------------------------------------------------------------+
int GetTrendDirection()
{
    double ema[], rsi[];
    ArraySetAsSeries(ema, true);
    ArraySetAsSeries(rsi, true);
    
    if(CopyBuffer(EMAHandle, 0, 0, 3, ema) <= 0 ||
       CopyBuffer(RSIHandle, 0, 0, 3, rsi) <= 0)
        return 0;
    
    double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    // Trend conditions
    bool bullishTrend = (currentPrice > ema[0]) && (ema[0] > ema[1]) && (rsi[0] > 50);
    bool bearishTrend = (currentPrice < ema[0]) && (ema[0] < ema[1]) && (rsi[0] < 50);
    
    if(bullishTrend)
        return 1;  // Bullish
    else if(bearishTrend)
        return -1; // Bearish
    
    return 0; // No clear trend
}

//+------------------------------------------------------------------+
//| Get entry signal from lower timeframe                            |
//+------------------------------------------------------------------+
int GetEntrySignal(int trendDirection)
{
    double ema[], rsi[];
    ArraySetAsSeries(ema, true);
    ArraySetAsSeries(rsi, true);
    
    if(CopyBuffer(EMAEntryHandle, 0, 0, 3, ema) <= 0 ||
       CopyBuffer(RSIEntryHandle, 0, 0, 3, rsi) <= 0)
        return 0;
    
    double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    if(trendDirection == 1) // Looking for buy signals
    {
        // Buy conditions: Price above EMA, RSI > 50 and rising
        if(currentPrice > ema[0] && rsi[0] > 50 && rsi[0] > rsi[1])
            return 1;
    }
    else if(trendDirection == -1) // Looking for sell signals
    {
        // Sell conditions: Price below EMA, RSI < 50 and falling
        if(currentPrice < ema[0] && rsi[0] < 50 && rsi[0] < rsi[1])
            return -1;
    }
    
    return 0;
}

//+------------------------------------------------------------------+
//| Open buy order                                                   |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
    double atr = GetATR(EntryTimeframe);
    if(atr <= 0) return;
    
    double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double stopLoss = ask - (atr * ATRStopMultiplier);
    double takeProfit = ask + (atr * ATRTargetMultiplier);
    
    double lotSize = CalculatePositionSize(ask - stopLoss);
    
    if(lotSize > 0)
    {
        if(Trade.Buy(lotSize, Symbol(), ask, stopLoss, takeProfit, TradeComment))
        {
            Print("BUY order opened: Lot=", lotSize, " Entry=", ask, " SL=", stopLoss, " TP=", takeProfit);
        }
        else
        {
            Print("Failed to open BUY order. Error: ", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| Open sell order                                                  |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
    double atr = GetATR(EntryTimeframe);
    if(atr <= 0) return;
    
    double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double stopLoss = bid + (atr * ATRStopMultiplier);
    double takeProfit = bid - (atr * ATRTargetMultiplier);
    
    double lotSize = CalculatePositionSize(stopLoss - bid);
    
    if(lotSize > 0)
    {
        if(Trade.Sell(lotSize, Symbol(), bid, stopLoss, takeProfit, TradeComment))
        {
            Print("SELL order opened: Lot=", lotSize, " Entry=", bid, " SL=", stopLoss, " TP=", takeProfit);
        }
        else
        {
            Print("Failed to open SELL order. Error: ", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk management                 |
//+------------------------------------------------------------------+
double CalculatePositionSize(double riskInPrice)
{
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * RiskPercentPerTrade / 100.0;
    double maxPositionValue = accountBalance * MaxPositionSizePercent / 100.0;
    
    double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
    
    if(tickValue <= 0 || tickSize <= 0 || riskInPrice <= 0)
        return 0;
    
    // Calculate lot size based on risk
    double riskInTicks = riskInPrice / tickSize;
    double lotSize = riskAmount / (riskInTicks * tickValue);
    
    // Apply maximum position size constraint
    double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double maxLotsByValue = maxPositionValue / (currentPrice * SymbolInfoDouble(Symbol(), SYMBOL_TRADE_CONTRACT_SIZE));
    lotSize = MathMin(lotSize, maxLotsByValue);
    
    // Normalize lot size
    lotSize = MathMax(minLot, lotSize);
    lotSize = MathMin(maxLot, lotSize);
    lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;
    
    return lotSize;
}

//+------------------------------------------------------------------+
//| Get ATR value                                                    |
//+------------------------------------------------------------------+
double GetATR(ENUM_TIMEFRAMES timeframe)
{
    int handle = (timeframe == TrendTimeframe) ? ATRHandle : ATREntryHandle;
    
    double atr[];
    ArraySetAsSeries(atr, true);
    
    if(CopyBuffer(handle, 0, 0, 2, atr) <= 0)
        return 0;
    
    return atr[0];
}

//+------------------------------------------------------------------+
//| Update trailing stops                                            |
//+------------------------------------------------------------------+
void UpdateTrailingStops()
{
    double atr = GetATR(EntryTimeframe);
    if(atr <= 0) return;
    
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetTicket(i) <= 0) continue;
        if(PositionGetInteger(POSITION_MAGIC) != MagicNumber) continue;
        if(PositionGetString(POSITION_SYMBOL) != Symbol()) continue;
        
        ulong ticket = PositionGetInteger(POSITION_TICKET);
        ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        double currentSL = PositionGetDouble(POSITION_SL);
        double currentTP = PositionGetDouble(POSITION_TP);
        
        double newSL = 0;
        
        if(posType == POSITION_TYPE_BUY)
        {
            double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
            newSL = currentPrice - (atr * TrailingStopATR);
            
            if(newSL > currentSL && newSL > openPrice)
            {
                Trade.PositionModify(ticket, newSL, currentTP);
            }
        }
        else if(posType == POSITION_TYPE_SELL)
        {
            double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
            newSL = currentPrice + (atr * TrailingStopATR);
            
            if(newSL < currentSL && newSL < openPrice)
            {
                Trade.PositionModify(ticket, newSL, currentTP);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Count open positions for this EA                                 |
//+------------------------------------------------------------------+
int CountOpenPositions()
{
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetTicket(i) <= 0) continue;
        if(PositionGetInteger(POSITION_MAGIC) == MagicNumber &&
           PositionGetString(POSITION_SYMBOL) == Symbol())
            count++;
    }
    return count;
}

//+------------------------------------------------------------------+
//| Close all positions                                              |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetTicket(i) <= 0) continue;
        if(PositionGetInteger(POSITION_MAGIC) == MagicNumber &&
           PositionGetString(POSITION_SYMBOL) == Symbol())
        {
            Trade.PositionClose(PositionGetInteger(POSITION_TICKET));
        }
    }
}
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
    // Only process deal additions (trade completions)
    if(trans.type != TRADE_TRANSACTION_DEAL_ADD) return;
    if(trans.symbol != Symbol()) return;
    
    if(HistoryDealSelect(trans.deal))
    {
        long dealMagic = HistoryDealGetInteger(trans.deal, DEAL_MAGIC);
        if(dealMagic != MagicNumber) return;
        
        double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
        if(profit != 0)
        {
            DailyPnL += profit;
            
            if(profit < 0)
            {
                ConsecutiveLosses++;
                if(ConsecutiveLosses >= MaxConsecutiveLosses)
                {
                    TradingPaused = true;
                    Print("Trading paused after ", ConsecutiveLosses, " consecutive losses");
                }
            }
            else
            {
                ConsecutiveLosses = 0;
                TradingPaused = false;
            }
            
            Print("Trade completed. Profit: $", DoubleToString(profit, 2), 
                  " Daily P&L: $", DoubleToString(DailyPnL, 2));
        }
    }
}
