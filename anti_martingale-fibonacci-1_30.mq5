//+------------------------------------------------------------------+
//|                                    anti-martingale-fibonacci.mq5 |
//|                                  Copyright 2023, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.40"

#include <Trade\Trade.mqh>

//--- Input Parameters
input bool autoCheckTrend = true;          // Enable automatic trend checking
input bool isSkipBonus = true;            // Skip bonus profit target (optional)
input string manualStartTrend = "BUY";     // Manual trend direction (BUY/SELL)
input double initAccountBalance = 10000;    // Initial account balance (0 for actual balance)
input double takeProfit = 0.005;           // Profit target as % of balance
input double targetDay = 1000;              // Daily profit target
input double initialLot = 0.05;            // Initial lot size
input double maxLot = 1.0;                 // Maximum lot size
input ENUM_TIMEFRAMES timeframe = PERIOD_M1; // Timeframe (M1)

//--- Global Variables
int FiboSeq[11] = {1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89}; // Fibonacci sequence
bool g_isFinishing = false;                               // Flag for daily target reached
double g_targetDay = targetDay;                           // Daily target
double g_collectTargetDay = 0;                            // Current daily profit/loss
double g_targetProfit = 0;                                // Profit target per trade
double g_accountBalance = 0;                              // Account balance
datetime g_lastPrintTime = 0;                             // Last print timestamp
datetime lastTradingDay = 0;                              // Last trading day
ulong currentTicketId = 0;                                // Current position ticket
int count = 1;                                            // Fibonacci step
bool startBonus = false;                                  // Bonus mode flag
bool startBonusDone = false;                              // Bonus completion flag
bool skipBonus = isSkipBonus;                             // Skip bonus flag
datetime timeBonus = 0;                                   // Bonus start time
int adxHandle = INVALID_HANDLE;                           // ADX handle
int atrHandle = INVALID_HANDLE;                           // ATR handle

CTrade Trade;        
//+------------------------------------------------------------------+
//| KeyValuePair Structure                                           |
//+------------------------------------------------------------------+
struct KeyValuePair
{
    ulong ticketId;
    string orderType;
    double lot;
    double targetProfit;
    double receiveProfit;
    bool isTrendTrade;
};                                     // Trade object
KeyValuePair pairs[];                                     // Trade data array

//--- Dynamic Parameters Based on Symbol
int maFastPeriod, maSlowPeriod, adxPeriod, rsiPeriod, bbPeriod;
double sidewayRSILower, sidewayRSIUpper;

//+------------------------------------------------------------------+
//| Expert Initialization Function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    string symbol = Symbol();
    if(symbol == "BTCUSD" || symbol == "BTCUSDc")
    {
        maFastPeriod = 10; maSlowPeriod = 30; adxPeriod = 7; rsiPeriod = 9; bbPeriod = 14;
        sidewayRSILower = 40.0; sidewayRSIUpper = 60.0; // Wider range for flexibility
    }
    else if(symbol == "XAUUSD" || symbol == "XAUUSDc")
    {
        maFastPeriod = 7; maSlowPeriod = 21; adxPeriod = 10; rsiPeriod = 14; bbPeriod = 20;
        sidewayRSILower = 40.0; sidewayRSIUpper = 60.0;
    }
    else
    {
        Print("Symbol not supported: ", symbol);
        return INIT_FAILED;
    }

    g_accountBalance = initAccountBalance == 0 ? AccountInfoDouble(ACCOUNT_BALANCE) : initAccountBalance;
    g_targetProfit = g_accountBalance * takeProfit;

    Print("******************************START_INIT******************************");
    Print("Target day: ", g_targetDay, " | Initial lot size: ", initialLot, " | Max lot: ", maxLot);
    Print("Account balance: ", g_accountBalance, " | Profit target per trade: ", g_targetProfit);
    Print("******************************END_INIT******************************");

    adxHandle = iADX(Symbol(), timeframe, adxPeriod);
    atrHandle = iATR(Symbol(), timeframe, 14);
    if(adxHandle == INVALID_HANDLE || atrHandle == INVALID_HANDLE)
    {
        Print("Error creating indicator handle");
        return INIT_FAILED;
    }

    Trade.SetDeviationInPoints(10);
    Trade.SetTypeFilling(ORDER_FILLING_FOK);
    Trade.SetAsyncMode(false);
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert Deinitialization Function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(adxHandle != INVALID_HANDLE) IndicatorRelease(adxHandle);
    if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
}

//+------------------------------------------------------------------+
//| Expert Tick Function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    datetime currentDay = TimeCurrent() / 86400 * 86400;
    if(currentDay != lastTradingDay && lastTradingDay != 0)
    {
        g_collectTargetDay = 0; g_isFinishing = false; count = 1; resetPairs();
        Print("***************NEW_TRADING_DAY***************");
    }
    lastTradingDay = currentDay;

    if(g_collectTargetDay >= g_targetDay || g_collectTargetDay <= -g_targetDay * 1.5)
    {
        if(!g_isFinishing)
        {
            g_isFinishing = true;
            Print("***************DONE_TARGET_DAY***************");
            Print("Current Target Day: ", g_collectTargetDay); printPairs();
            for(int i = PositionsTotal() - 1; i >= 0; i--) Trade.PositionClose(PositionGetTicket(i));
        }
        return;
    }
    checkAndOpenAdditionalOrders();
}

//+------------------------------------------------------------------+
//| Open a New Order                                                 |
//+------------------------------------------------------------------+
bool OpenOrder()
{
    int trend = autoCheckTrend ? checkTrend() : (manualStartTrend == "BUY" ? 1 : -1);
    bool isBuyTrend = false;
    double newLots = MathMin(initialLot * FiboSeq[count], maxLot);
    bool isTrendTrade = false;

    if(trend != 0) // Trend detected
    {
        isBuyTrend = trend == 1;
        isTrendTrade = true;
    }
    else // Sideways market
    {
        double rsi = getRSI();
        if(rsi < 30) // Oversold, buy
        {
            isBuyTrend = true; 
            isTrendTrade = false;
        }
        else if(rsi > 70) // Overbought, sell
        {
            isBuyTrend = false; 
            isTrendTrade = false;
        }
        else return false; // No clear signal
    }

    double atrValue = getATR();
    double currentPrice = SymbolInfoDouble(Symbol(), isBuyTrend ? SYMBOL_ASK : SYMBOL_BID);
    double sl = isBuyTrend ? currentPrice - atrValue * 2 : currentPrice + atrValue * 2;
    double tp = isBuyTrend ? currentPrice + atrValue * 4 : currentPrice - atrValue * 4;

    isBuyTrend
        ? Trade.Buy(newLots, Symbol(), currentPrice, sl, tp, "Open BUY Order")
        : Trade.Sell(newLots, Symbol(), currentPrice, sl, tp, "Open SELL Order");
    currentTicketId = Trade.ResultOrder();

    if(currentTicketId > 0)
    {
        addKeyValuePair(currentTicketId, newLots, g_targetProfit * (isTrendTrade ? FiboSeq[count] : 1), 
                        isBuyTrend ? "BUY" : "SELL", 0.0, isTrendTrade);
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check and Manage Orders                                          |
//+------------------------------------------------------------------+
void checkAndOpenAdditionalOrders()
{
    if(currentTicketId == 0 || !PositionSelectByTicket(currentTicketId))
    {
        OpenOrder();
        return;
    }

    double profit = PositionGetDouble(POSITION_PROFIT);
    double targetProfit = g_targetProfit * FiboSeq[count];
    double targetSL = -g_targetProfit * FiboSeq[count - 1];

    datetime currentTime = TimeCurrent();
    if(currentTime - g_lastPrintTime >= 20)
    {
        Print("***************START_DONE***************");
        Print("Profit: ", profit, " | Target Profit: ", targetProfit);
        Print("Positive: ", (profit >= targetProfit ? "true" : "false"), " | Negative: ", (profit <= targetSL ? "true" : "false"));
        Print("Target Day: ", g_targetDay, " | Current Target Day: ", g_collectTargetDay);
        printPairs();
        Print("***************END_DONE***************");
        g_lastPrintTime = currentTime;
    }

    if(profit >= targetProfit)
    {
        Trade.PositionClose(currentTicketId); g_collectTargetDay += profit;
        updateValuePair(currentTicketId, profit);
        if(getPairValueByTicketIt(currentTicketId).isTrendTrade) count = MathMin(count + 1, ArraySize(FiboSeq) - 1);
        currentTicketId = 0; startBonusDone = false; OpenOrder();
    }
    else if(profit <= targetSL)
    {
        Trade.PositionClose(currentTicketId); g_collectTargetDay += profit;
        updateValuePair(currentTicketId, profit); count = 1;
        currentTicketId = 0; startBonusDone = false; OpenOrder();
    }
}

//+------------------------------------------------------------------+
//| Check Trend                                                      |
//+------------------------------------------------------------------+
int checkTrend()
{
    if(isSideway())
    {
        return 0;
    }

    bool adxStrong = isStrongTrend();
    if(!adxStrong) return 0;

    bool maUptrend = isUptrend();
    bool priceActionUptrend = isUptrendPriceAction();
    bool rsiCondition = isRSICondition();

    if(maUptrend && priceActionUptrend && rsiCondition) return 1;
    else if(!maUptrend && !priceActionUptrend && rsiCondition) return -1;
    return 0;
}

//+------------------------------------------------------------------+
//| Optimized Is Sideway                                             |
//+------------------------------------------------------------------+
bool isSideway()
{
    // ADX
    double adx[], plusDI[], minusDI[];
    if(!getADXValues(adx, plusDI, minusDI, 2)) return false;
    double adxAvg[];
    ArraySetAsSeries(adxAvg, true);
    if(CopyBuffer(adxHandle, 0, 0, 50, adxAvg) <= 0) return false;
    double avgAdx = ArraySum(adxAvg) / 50;
    bool adxCondition = adx[0] < MathMin(avgAdx * 0.8, 25.0);

    // RSI
    double rsi[];
    ArraySetAsSeries(rsi, true);
    int rsiHandle = iRSI(Symbol(), timeframe, rsiPeriod, PRICE_CLOSE);
    if(CopyBuffer(rsiHandle, 0, 0, 2, rsi) <= 0) return false;
    bool rsiCondition = rsi[0] >= sidewayRSILower && rsi[0] <= sidewayRSIUpper;

    // Bollinger Bands
    double bbUpper[], bbMiddle[], bbLower[];
    ArraySetAsSeries(bbUpper, true); ArraySetAsSeries(bbMiddle, true); ArraySetAsSeries(bbLower, true);
    int bbHandle = iBands(Symbol(), timeframe, bbPeriod, 0, 2, PRICE_CLOSE);
    if(CopyBuffer(bbHandle, 0, 0, 20, bbMiddle) <= 0 || CopyBuffer(bbHandle, 1, 0, 20, bbUpper) <= 0 || 
       CopyBuffer(bbHandle, 2, 0, 20, bbLower) <= 0) return false;
    double avgWidth = 0;
    for(int i = 0; i < 20; i++) avgWidth += (bbUpper[i] - bbLower[i]) / bbMiddle[i];
    avgWidth /= 20;
    bool bbCondition = (bbUpper[0] - bbLower[0]) / bbMiddle[0] < avgWidth * 1.2;

    // ATR
    double atr[], atrAvg[];
    ArraySetAsSeries(atr, true); ArraySetAsSeries(atrAvg, true);
    if(CopyBuffer(atrHandle, 0, 0, 2, atr) <= 0 || CopyBuffer(atrHandle, 0, 0, 14, atrAvg) <= 0) return false;
    double avgAtr = ArraySum(atrAvg) / 14;
    bool atrCondition = atr[0] < avgAtr;

    return adxCondition && rsiCondition && bbCondition && atrCondition;
}

//+------------------------------------------------------------------+
//| Is Strong Trend                                                  |
//+------------------------------------------------------------------+
bool isStrongTrend()
{
    double adx[], plusDI[], minusDI[];
    if(!getADXValues(adx, plusDI, minusDI, 2)) return false;
    return adx[0] > 25.0 && MathAbs(plusDI[0] - minusDI[0]) > 5.0;
}

//+------------------------------------------------------------------+
//| Is Uptrend                                                       |
//+------------------------------------------------------------------+
bool isUptrend()
{
    double maFast[], maSlow[];
    ArraySetAsSeries(maFast, true); ArraySetAsSeries(maSlow, true);
    int maFastHandle = iMA(Symbol(), timeframe, maFastPeriod, 0, MODE_SMA, PRICE_CLOSE);
    int maSlowHandle = iMA(Symbol(), timeframe, maSlowPeriod, 0, MODE_SMA, PRICE_CLOSE);
    if(CopyBuffer(maFastHandle, 0, 0, 2, maFast) <= 0 || CopyBuffer(maSlowHandle, 0, 0, 2, maSlow) <= 0) return false;
    return maFast[0] > maSlow[0];
}

//+------------------------------------------------------------------+
//| Is Uptrend Price Action                                          |
//+------------------------------------------------------------------+
bool isUptrendPriceAction()
{
    double high[], low[];
    ArraySetAsSeries(high, true); ArraySetAsSeries(low, true);
    if(CopyHigh(Symbol(), timeframe, 0, 2, high) <= 0 || CopyLow(Symbol(), timeframe, 0, 2, low) <= 0) return false;
    return high[0] > high[1] && low[0] > low[1];
}

//+------------------------------------------------------------------+
//| Is RSI Condition                                                 |
//+------------------------------------------------------------------+
bool isRSICondition()
{
    double rsi[];
    ArraySetAsSeries(rsi, true);
    int rsiHandle = iRSI(Symbol(), timeframe, rsiPeriod, PRICE_CLOSE);
    if(CopyBuffer(rsiHandle, 0, 0, 1, rsi) <= 0) return false;
    return rsi[0] > 30 && rsi[0] < 70;
}

//+------------------------------------------------------------------+
//| Get RSI                                                          |
//+------------------------------------------------------------------+
double getRSI()
{
    double rsi[];
    ArraySetAsSeries(rsi, true);
    int rsiHandle = iRSI(Symbol(), timeframe, rsiPeriod, PRICE_CLOSE);
    if(CopyBuffer(rsiHandle, 0, 0, 1, rsi) <= 0) return 50.0;
    return rsi[0];
}

//+------------------------------------------------------------------+
//| Get ATR                                                          |
//+------------------------------------------------------------------+
double getATR()
{
    double atr[];
    ArraySetAsSeries(atr, true);
    if(CopyBuffer(atrHandle, 0, 0, 1, atr) <= 0) return 0.0;
    return atr[0];
}

//+------------------------------------------------------------------+
//| Get ADX Values                                                   |
//+------------------------------------------------------------------+
bool getADXValues(double &adx[], double &plusDI[], double &minusDI[], int bars)
{
    ArraySetAsSeries(adx, true); ArraySetAsSeries(plusDI, true); ArraySetAsSeries(minusDI, true);
    if(CopyBuffer(adxHandle, 0, 0, bars, adx) <= 0 || CopyBuffer(adxHandle, 1, 0, bars, plusDI) <= 0 || 
       CopyBuffer(adxHandle, 2, 0, bars, minusDI) <= 0) return false;
    return true;
}

//+------------------------------------------------------------------+
//| Add KeyValuePair                                                 |
//+------------------------------------------------------------------+
void addKeyValuePair(ulong ticketId, double lot, double targetProfit, string orderType, double receiveProfit, bool isTrendTrade)
{
    int size = ArraySize(pairs);
    ArrayResize(pairs, size + 1);
    pairs[size].lot = lot; pairs[size].ticketId = ticketId; pairs[size].targetProfit = targetProfit;
    pairs[size].orderType = orderType; pairs[size].receiveProfit = receiveProfit; pairs[size].isTrendTrade = isTrendTrade;
}

//+------------------------------------------------------------------+
//| Update KeyValuePair                                              |
//+------------------------------------------------------------------+
void updateValuePair(ulong ticketId, double receiveProfit)
{
    for(int i = 0; i < ArraySize(pairs); i++)
        if(pairs[i].ticketId == ticketId) { pairs[i].receiveProfit = receiveProfit; return; }
}

//+------------------------------------------------------------------+
//| Reset Pairs                                                      |
//+------------------------------------------------------------------+
void resetPairs()
{
    ArrayResize(pairs, 0);
}

//+------------------------------------------------------------------+
//| Print Pairs                                                      |
//+------------------------------------------------------------------+
void printPairs()
{
    string output = "\n";
    for(int i = 0; i < ArraySize(pairs); i++)
    {
        output += pairs[i].ticketId + " orderType: " + pairs[i].orderType + " lot: " + DoubleToString(pairs[i].lot, 2) + 
                  " targetProfit: " + DoubleToString(pairs[i].targetProfit, 2) + " receiveProfit: " + DoubleToString(pairs[i].receiveProfit, 2) + 
                  " isTrendTrade: " + (pairs[i].isTrendTrade ? "true" : "false") + (i < ArraySize(pairs) - 1 ? "\n" : "");
    }
    Print("targetChild: ", output);
}

//+------------------------------------------------------------------+
//| Get Pair Value By Ticket Id                                      |
//+------------------------------------------------------------------+
KeyValuePair getPairValueByTicketIt(ulong ticketId)
{
    for(int i = 0; i < ArraySize(pairs); i++)
        if(pairs[i].ticketId == ticketId) return pairs[i];
    KeyValuePair defaultPair = {0, "NONE", 0.0, 0.0, 0.0, false};
    return defaultPair;
}

//+------------------------------------------------------------------+