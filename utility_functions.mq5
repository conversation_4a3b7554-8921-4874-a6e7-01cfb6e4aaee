//+------------------------------------------------------------------+
//| Utility Functions for Enhanced Trading EA                       |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Check if within trading session                                 |
//+------------------------------------------------------------------+
bool IsWithinTradingSession()
{
   datetime currentTime = TimeCurrent();
   MqlDateTime timeStruct;
   TimeToStruct(currentTime, timeStruct);
   
   // Convert time strings to minutes
   string startParts[];
   string endParts[];
   StringSplit(TradingStartTime, ':', startParts);
   StringSplit(TradingEndTime, ':', endParts);
   
   int startMinutes = (int)StringToInteger(startParts[0]) * 60 + (int)StringToInteger(startParts[1]);
   int endMinutes = (int)StringToInteger(endParts[0]) * 60 + (int)StringToInteger(endParts[1]);
   int currentMinutes = timeStruct.hour * 60 + timeStruct.min;
   
   if(startMinutes <= endMinutes)
   {
      return (currentMinutes >= startMinutes && currentMinutes <= endMinutes);
   }
   else // Overnight session
   {
      return (currentMinutes >= startMinutes || currentMinutes <= endMinutes);
   }
}

//+------------------------------------------------------------------+
//| Check if it's news time (simplified implementation)             |
//+------------------------------------------------------------------+
bool IsNewsTime()
{
   // This is a simplified implementation
   // In a real EA, you would integrate with a news calendar API
   
   datetime currentTime = TimeCurrent();
   MqlDateTime timeStruct;
   TimeToStruct(currentTime, timeStruct);
   
   // Avoid trading during typical high-impact news times (example)
   // 8:30 AM EST (13:30 GMT) - US news
   // 2:00 PM EST (19:00 GMT) - FOMC announcements
   
   int currentHour = timeStruct.hour;
   int currentMin = timeStruct.min;
   
   // Check for 8:30 AM EST (13:30 GMT) ± NewsFilterMinutes
   if(currentHour == 13 && MathAbs(currentMin - 30) <= NewsFilterMinutes)
      return true;
      
   // Check for 2:00 PM EST (19:00 GMT) ± NewsFilterMinutes  
   if(currentHour == 19 && currentMin <= NewsFilterMinutes)
      return true;
      
   return false;
}

//+------------------------------------------------------------------+
//| Check if new day started                                         |
//+------------------------------------------------------------------+
void CheckNewDay()
{
   static int lastDay = -1;
   
   MqlDateTime timeStruct;
   TimeToStruct(TimeCurrent(), timeStruct);
   
   if(lastDay != timeStruct.day)
   {
      if(lastDay != -1) // Not first run
      {
         // New day started, reset daily statistics
         PrintDailyReport();
         ResetDailyStats();
      }
      lastDay = timeStruct.day;
   }
}

//+------------------------------------------------------------------+
//| Reset daily statistics                                           |
//+------------------------------------------------------------------+
void ResetDailyStats()
{
   g_TodayStats.date = TimeCurrent();
   g_TodayStats.profit = 0;
   g_TodayStats.totalTrades = 0;
   g_TodayStats.winningTrades = 0;
   g_TodayStats.losingTrades = 0;
   g_TodayStats.maxDrawdown = 0;
   
   g_DailyProfit = 0;
   g_DailyTargetReached = false;
   g_ConsecutiveLosses = 0;
   g_ConsecutiveWins = 0;
   g_MaxDrawdownReached = 0;
   
   // Re-enable trading if it was disabled
   if(!g_TradingEnabled && TimeCurrent() > g_TradingPauseUntil)
   {
      g_TradingEnabled = true;
      Print("Trading re-enabled for new day");
   }
}

//+------------------------------------------------------------------+
//| Update daily statistics                                          |
//+------------------------------------------------------------------+
void UpdateDailyStats()
{
   double currentProfit = 0;
   
   // Calculate total profit from all positions
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         {
            currentProfit += PositionGetDouble(POSITION_PROFIT);
         }
      }
   }
   
   g_DailyProfit = currentProfit;
   
   // Check if daily target reached
   if(!g_DailyTargetReached && g_DailyProfit >= g_DailyTarget)
   {
      g_DailyTargetReached = true;
      Print("DAILY TARGET REACHED! Profit: $", DoubleToString(g_DailyProfit, 2));
      Print("Trading suspended for today");
   }
}

//+------------------------------------------------------------------+
//| Handle trailing stop                                             |
//+------------------------------------------------------------------+
void HandleTrailingStop(int posIndex, double currentPrice)
{
   double atr[];
   ArraySetAsSeries(atr, true);
   
   if(CopyBuffer(g_handleATR, 0, 0, 1, atr) < 1)
      return;
      
   double atrValue = atr[0];
   double newStopLoss = 0;
   
   if(g_CurrentPositions[posIndex].type == POSITION_TYPE_BUY)
   {
      newStopLoss = currentPrice - (atrValue * 1.5); // 1.5 ATR trailing
      
      if(newStopLoss > g_CurrentPositions[posIndex].initialSL)
      {
         newStopLoss = NormalizeDouble(newStopLoss, Digits());
         
         if(Trade.PositionModify(g_CurrentPositions[posIndex].ticket, newStopLoss, 
                                g_CurrentPositions[posIndex].initialTP))
         {
            g_CurrentPositions[posIndex].initialSL = newStopLoss;
            
            if(EnableDetailedLogging)
            {
               Print("Trailing stop updated for BUY position: ", 
                     g_CurrentPositions[posIndex].ticket, " New SL: ", newStopLoss);
            }
         }
      }
   }
   else if(g_CurrentPositions[posIndex].type == POSITION_TYPE_SELL)
   {
      newStopLoss = currentPrice + (atrValue * 1.5); // 1.5 ATR trailing
      
      if(newStopLoss < g_CurrentPositions[posIndex].initialSL)
      {
         newStopLoss = NormalizeDouble(newStopLoss, Digits());
         
         if(Trade.PositionModify(g_CurrentPositions[posIndex].ticket, newStopLoss, 
                                g_CurrentPositions[posIndex].initialTP))
         {
            g_CurrentPositions[posIndex].initialSL = newStopLoss;
            
            if(EnableDetailedLogging)
            {
               Print("Trailing stop updated for SELL position: ", 
                     g_CurrentPositions[posIndex].ticket, " New SL: ", newStopLoss);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Handle breakeven                                                 |
//+------------------------------------------------------------------+
void HandleBreakeven(int posIndex, double currentPrice)
{
   double openPrice = g_CurrentPositions[posIndex].openPrice;
   double stopDistance = MathAbs(openPrice - g_CurrentPositions[posIndex].initialSL);
   double profitDistance = 0;
   
   if(g_CurrentPositions[posIndex].type == POSITION_TYPE_BUY)
   {
      profitDistance = currentPrice - openPrice;
   }
   else if(g_CurrentPositions[posIndex].type == POSITION_TYPE_SELL)
   {
      profitDistance = openPrice - currentPrice;
   }
   
   // Move to breakeven when profit is 1.5x stop distance
   if(profitDistance >= (stopDistance * 1.5))
   {
      double newStopLoss = NormalizeDouble(openPrice, Digits());
      
      // Only move if current SL is worse than breakeven
      bool shouldMove = false;
      
      if(g_CurrentPositions[posIndex].type == POSITION_TYPE_BUY && 
         newStopLoss > g_CurrentPositions[posIndex].initialSL)
      {
         shouldMove = true;
      }
      else if(g_CurrentPositions[posIndex].type == POSITION_TYPE_SELL && 
              newStopLoss < g_CurrentPositions[posIndex].initialSL)
      {
         shouldMove = true;
      }
      
      if(shouldMove)
      {
         if(Trade.PositionModify(g_CurrentPositions[posIndex].ticket, newStopLoss, 
                                g_CurrentPositions[posIndex].initialTP))
         {
            g_CurrentPositions[posIndex].initialSL = newStopLoss;
            
            if(EnableDetailedLogging)
            {
               Print("Position moved to breakeven: ", g_CurrentPositions[posIndex].ticket);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Update trade statistics                                          |
//+------------------------------------------------------------------+
void UpdateTradeStatistics(int posIndex)
{
   // This function is called when a position is closed
   double profit = 0;
   
   // Get the final profit (this is simplified - in real implementation 
   // you'd need to track this more carefully)
   for(int i = 0; i < OrdersHistoryTotal(); i++)
   {
      if(OrderGetTicket(i) == g_CurrentPositions[posIndex].ticket)
      {
         profit = OrderGetDouble(ORDER_PROFIT);
         break;
      }
   }
   
   g_TodayStats.totalTrades++;
   g_TodayStats.profit += profit;
   
   if(profit > 0)
   {
      g_TodayStats.winningTrades++;
      g_ConsecutiveWins++;
      g_ConsecutiveLosses = 0;
   }
   else
   {
      g_TodayStats.losingTrades++;
      g_ConsecutiveLosses++;
      g_ConsecutiveWins = 0;
      
      // Check if need to pause trading
      if(g_ConsecutiveLosses >= MaxConsecutiveLosses)
      {
         g_TradingPauseUntil = TimeCurrent() + (3600 * 2); // Pause for 2 hours
         Print("Trading paused due to ", g_ConsecutiveLosses, " consecutive losses");
         Print("Trading will resume at: ", TimeToString(g_TradingPauseUntil));
      }
   }
}

//+------------------------------------------------------------------+
//| Remove position from tracking array                             |
//+------------------------------------------------------------------+
void RemovePositionFromArray(int index)
{
   for(int i = index; i < g_PositionCount - 1; i++)
   {
      g_CurrentPositions[i] = g_CurrentPositions[i + 1];
   }
   
   g_PositionCount--;
   ArrayResize(g_CurrentPositions, g_PositionCount);
}
